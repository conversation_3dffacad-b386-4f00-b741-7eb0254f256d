import React, { useState, useRef, useEffect } from 'react';
import { useLanguage } from '../../LanguageContext.jsx';

const InventoryManagement = ({ 
  products,
  setProducts,
  suppliers,
  categories,
  setCategories,
  showProductModal,
  setShowProductModal,
  showCategoryModal,
  setShowCategoryModal,
  newProduct,
  setNewProduct,
  newCategory,
  setNewCategory,
  editingCategory,
  setEditingCategory,
  selectedProducts,
  setSelectedProducts,
  productSearch,
  setProductSearch,
  selectedCategory,
  setSelectedCategory,
  selectedStatus,
  setSelectedStatus,
  showToast,
  formatPrice,
  t,
  currentLanguage,
  currentUser,
  toggleSelectAll,
  toggleSelectItem,
  deleteSelectedItems,
  saveProducts,
  saveCategories
}) => {

  // Filter products based on current filters
  const filteredProducts = products.filter(product => {
    const matchesSearch = !productSearch || 
      product.name.toLowerCase().includes(productSearch.toLowerCase()) ||
      product.barcode.toLowerCase().includes(productSearch.toLowerCase());
    const matchesCategory = !selectedCategory || product.category === selectedCategory;
    const matchesStatus = !selectedStatus || 
      (selectedStatus === 'low-stock' && product.quantity <= product.minStock) ||
      (selectedStatus === 'out-of-stock' && product.quantity === 0) ||
      (selectedStatus === 'in-stock' && product.quantity > 0);

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const openProductModal = () => {
    setNewProduct({
      id: '',
      name: '',
      barcode: '',
      category: '',
      buyPrice: 0,
      sellPrice: 0,
      quantity: 0,
      minStock: 5,
      supplier: '',
      description: ''
    });
    setShowProductModal(true);
  };

  const saveProduct = () => {
    if (!newProduct.name.trim()) {
      showToast(`⚠️ ${t('productNameRequired', 'اسم المنتج مطلوب')}`, 'warning', 3000);
      return;
    }

    if (!newProduct.barcode.trim()) {
      showToast(`⚠️ ${t('barcodeRequired', 'الباركود مطلوب')}`, 'warning', 3000);
      return;
    }

    // Check if barcode already exists
    const existingProduct = products.find(p => p.barcode === newProduct.barcode && p.id !== newProduct.id);
    if (existingProduct) {
      showToast(`❌ ${t('barcodeExists', 'الباركود موجود مسبقاً')}`, 'error', 3000);
      return;
    }

    const productToSave = {
      ...newProduct,
      id: newProduct.id || 'PROD-' + Date.now(),
      buyPrice: parseFloat(newProduct.buyPrice) || 0,
      sellPrice: parseFloat(newProduct.sellPrice) || 0,
      quantity: parseInt(newProduct.quantity) || 0,
      minStock: parseInt(newProduct.minStock) || 5
    };

    if (newProduct.id) {
      // Update existing product
      const updatedProducts = products.map(p => 
        p.id === newProduct.id ? productToSave : p
      );
      setProducts(updatedProducts);
      saveProducts(updatedProducts);
      showToast(`✅ ${t('productUpdated', 'تم تحديث المنتج')}`, 'success', 2000);
    } else {
      // Add new product
      const updatedProducts = [...products, productToSave];
      setProducts(updatedProducts);
      saveProducts(updatedProducts);
      showToast(`✅ ${t('productAdded', 'تم إضافة المنتج')}`, 'success', 2000);
    }

    setShowProductModal(false);
  };

  const editProduct = (product) => {
    setNewProduct(product);
    setShowProductModal(true);
  };

  const addCategory = () => {
    if (!newCategory.trim()) {
      showToast(`⚠️ ${t('categoryNameRequired', 'اسم الفئة مطلوب')}`, 'warning', 3000);
      return;
    }

    if (categories.includes(newCategory.trim())) {
      showToast(`❌ ${t('categoryExists', 'الفئة موجودة مسبقاً')}`, 'error', 3000);
      return;
    }

    const updatedCategories = [...categories, newCategory.trim()];
    setCategories(updatedCategories);
    saveCategories(updatedCategories);
    setNewCategory('');
    setShowCategoryModal(false);
    showToast(`✅ ${t('categoryAdded', 'تم إضافة الفئة')}`, 'success', 2000);
  };

  const deleteCategory = (categoryToDelete) => {
    const updatedCategories = categories.filter(cat => cat !== categoryToDelete);
    setCategories(updatedCategories);
    saveCategories(updatedCategories);
    showToast(`✅ ${t('categoryDeleted', 'تم حذف الفئة')}`, 'success', 2000);
  };

  return (
    <div className="inventory-page">
      <div className={`page-header ${currentLanguage !== 'ar' ? 'page-header-ltr-split' : ''}`}>
        <div className="page-title-section">
          <h1>📋 {t('inventory', 'المخزون')}</h1>
        </div>
        <div className="page-actions">
          <button 
            className="btn btn-primary"
            onClick={openProductModal}
          >
            <span className="btn-icon">➕</span>
            {t('newProduct', 'منتج جديد')}
          </button>
          <button 
            className="btn btn-secondary"
            onClick={() => setShowCategoryModal(true)}
          >
            <span className="btn-icon">🏷️</span>
            {t('manageCategories', 'إدارة الفئات')}
          </button>
        </div>
      </div>

      {/* Inventory Filters */}
      <div className="filters-section">
        <div className="filters-grid">
          <div className="filter-group">
            <label>{t('search', 'البحث')}</label>
            <input
              type="text"
              placeholder={t('searchProducts', 'البحث في المنتجات')}
              value={productSearch}
              onChange={(e) => setProductSearch(e.target.value)}
              className="filter-input"
            />
          </div>

          <div className="filter-group">
            <label>{t('category', 'الفئة')}</label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="filter-select"
            >
              <option value="">{t('allCategories', 'جميع الفئات')}</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>{t('status', 'الحالة')}</label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="filter-select"
            >
              <option value="">{t('allProducts', 'جميع المنتجات')}</option>
              <option value="in-stock">{t('inStock', 'متوفر')}</option>
              <option value="low-stock">{t('lowStock', 'مخزون قليل')}</option>
              <option value="out-of-stock">{t('outOfStock', 'نفد المخزون')}</option>
            </select>
          </div>
        </div>

        <div className="filters-actions">
          <button 
            className="btn btn-secondary"
            onClick={() => {
              setProductSearch('');
              setSelectedCategory('');
              setSelectedStatus('');
            }}
          >
            {t('clearFilters', 'مسح المرشحات')}
          </button>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedProducts.length > 0 && (
        <div className="bulk-actions">
          <span className="bulk-count">
            {selectedProducts.length} {t('itemsSelected', 'عنصر محدد')}
          </span>
          <button 
            className="btn btn-danger"
            onClick={() => deleteSelectedItems('products')}
          >
            {t('deleteSelected', 'حذف المحدد')}
          </button>
        </div>
      )}

      {/* Products Table */}
      <div className="table-container">
        <table className="data-table">
          <thead>
            <tr>
              <th>
                <input
                  type="checkbox"
                  checked={selectedProducts.length === filteredProducts.length && filteredProducts.length > 0}
                  onChange={() => toggleSelectAll('products', filteredProducts)}
                />
              </th>
              <th>{t('name', 'الاسم')}</th>
              <th>{t('barcode', 'الباركود')}</th>
              <th>{t('category', 'الفئة')}</th>
              <th>{t('supplier', 'المورد')}</th>
              <th>{t('buyPrice', 'سعر الشراء')}</th>
              <th>{t('sellPrice', 'سعر البيع')}</th>
              <th>{t('quantity', 'الكمية')}</th>
              <th>{t('minStock', 'الحد الأدنى')}</th>
              <th>{t('status', 'الحالة')}</th>
              <th>{t('actions', 'الإجراءات')}</th>
            </tr>
          </thead>
          <tbody>
            {filteredProducts.map(product => (
              <tr key={product.id}>
                <td>
                  <input
                    type="checkbox"
                    checked={selectedProducts.includes(product.id)}
                    onChange={() => toggleSelectItem('products', product.id)}
                  />
                </td>
                <td>{product.name}</td>
                <td>{product.barcode}</td>
                <td>{product.category}</td>
                <td>{product.supplier || '-'}</td>
                <td>{formatPrice(product.buyPrice)}</td>
                <td>{formatPrice(product.sellPrice)}</td>
                <td>
                  <span className={`quantity ${product.quantity <= product.minStock ? 'low-stock' : ''}`}>
                    {product.quantity}
                  </span>
                </td>
                <td>{product.minStock}</td>
                <td>
                  <span className={`status-badge ${
                    product.quantity === 0 ? 'status-out-of-stock' :
                    product.quantity <= product.minStock ? 'status-low-stock' :
                    'status-in-stock'
                  }`}>
                    {product.quantity === 0 ? t('outOfStock', 'نفد المخزون') :
                     product.quantity <= product.minStock ? t('lowStock', 'مخزون قليل') :
                     t('inStock', 'متوفر')}
                  </span>
                </td>
                <td>
                  <div className="action-buttons">
                    <button
                      className="btn btn-sm btn-warning"
                      onClick={() => editProduct(product)}
                      title={t('edit', 'تعديل')}
                    >
                      ✏️
                    </button>
                    {(currentUser.role === 'مدير' || currentUser.role === 'admin') && (
                      <button
                        className="btn btn-sm btn-danger"
                        onClick={() => deleteSelectedItems('products', [product.id])}
                        title={t('delete', 'حذف')}
                      >
                        🗑️
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Inventory Statistics */}
      <div className="inventory-stats">
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">📦</div>
            <div className="stat-content">
              <h3>{t('totalProducts', 'إجمالي المنتجات')}</h3>
              <div className="stat-value">{filteredProducts.length}</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">💰</div>
            <div className="stat-content">
              <h3>{t('totalInventoryValue', 'قيمة المخزون الإجمالية')}</h3>
              <div className="stat-value">
                {formatPrice(filteredProducts.reduce((sum, product) => 
                  sum + (product.quantity * product.buyPrice), 0))}
              </div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">⚠️</div>
            <div className="stat-content">
              <h3>{t('lowStockProducts', 'منتجات قليلة المخزون')}</h3>
              <div className="stat-value">
                {filteredProducts.filter(p => p.quantity <= p.minStock && p.quantity > 0).length}
              </div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">❌</div>
            <div className="stat-content">
              <h3>{t('outOfStockProducts', 'منتجات نفد مخزونها')}</h3>
              <div className="stat-value">
                {filteredProducts.filter(p => p.quantity === 0).length}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InventoryManagement;
