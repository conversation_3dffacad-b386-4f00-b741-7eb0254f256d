import{j as s}from"./react-vendor-03c8a839.js";import"./i18n-121a6c54.js";const e=({dashboardScannerInput:e,setDashboardScannerInput:a,dashboardScannerRef:c,dashboardLcdDisplay:t,setDashboardLcdDisplay:n,dashboardTotalDisplay:d,setDashboardTotalDisplay:i,products:l,handleDashboardScannerInput:r,handleDashboardScannerKeyPress:o,isValidScannedCode:h,setSalesInvoice:m,setShowSalesModal:v,setSalesScannerInput:u,navigateToPage:x,showToast:j,t:b,currentLanguage:N,savedInvoices:p,formatPrice:g,calculateDashboardStats:S})=>{const w=S();return s.jsxs("div",{className:"dashboard",children:[s.jsx("div",{className:"page-header "+("ar"!==N?"page-header-ltr-split":""),children:s.jsx("div",{className:"page-title-section",children:s.jsxs("h1",{children:["🏠 ",b("dashboard","لوحة التحكم")]})})}),s.jsx("div",{className:"dashboard-scanner-lcd-unified",children:s.jsxs("div",{className:"unified-frame",children:[s.jsxs("div",{className:"scanner-section",children:[s.jsxs("h3",{children:["📷 ",b("scanBarcode","مسح الباركود")," - ",s.jsx("span",{className:"scanner-status-active",children:b("active","نشط")})]}),s.jsxs("div",{className:"barcode-input-container",children:[s.jsx("span",{className:"barcode-icon",children:"📷"}),s.jsx("input",{type:"text",placeholder:b("scanBarcodeToAddProduct","امسح الباركود - اضغط Enter لفتح الفاتورة"),value:e,onChange:r,onKeyDown:o,onFocus:()=>{window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!0,window.barcodeShortcutManager.setShortcutsEnabled(!1),console.log("🔧 BARCODE FIX: Dashboard scanner focused - shortcuts disabled"))},onBlur:()=>{setTimeout((()=>{window.barcodeShortcutManager&&!window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0),console.log("🔧 BARCODE FIX: Dashboard scanner blurred - shortcuts re-enabled"))}),100)},className:"barcode-input",ref:c,autoFocus:!0})]}),s.jsx("div",{className:"barcode-actions",children:s.jsx("button",{type:"button",className:"btn btn-success btn-sm",onClick:s=>{s.preventDefault(),s.stopPropagation(),console.log("🏠 Dashboard: Manual button click - Opening invoice");const c=e.trim();if(c&&h(c)){const s=l.find((s=>s.barcode===c&&""!==c.trim()));if(s){console.log("🏠 Dashboard: Opening sales invoice with product:",s.name),a("");const e={invoiceNumber:"INV-"+Date.now(),date:(new Date).toISOString().split("T")[0],customerId:"GUEST",customerName:b("walkInCustomer","زبون عابر"),paymentMethod:"نقداً",items:[],total:0,discount:0,tax:0,finalTotal:0};u(""),m(e),v(!0),setTimeout((()=>{console.log("🏠 Dashboard: Adding product to new sales invoice:",s.name);const e={productId:s.id,productName:s.name,quantity:1,price:s.sellPrice||s.price,total:s.sellPrice||s.price};m((s=>{const a={...s,items:[...s.items,e]},c=a.items.reduce(((s,e)=>s+e.total),0);return a.total=c,a.finalTotal=c-a.discount+a.tax,a})),j(`✅ ${b("productAddedToInvoice","تم إضافة المنتج للفاتورة")}: ${s.name}`,"success",2e3)}),100)}else j(`❌ ${b("productNotFound","المنتج غير موجود")}: ${c}`,"error",3e3)}else j(`⚠️ ${b("invalidBarcode","باركود غير صحيح")}`,"warning",2e3)},children:b("openInvoice","فتح الفاتورة")})})]}),s.jsxs("div",{className:"lcd-section",children:[s.jsxs("h3",{children:["💰 ",b("lcdDisplay","شاشة العرض")]}),s.jsx("div",{className:"lcd-display",children:s.jsx("div",{className:"lcd-screen",children:t?s.jsxs("div",{className:"lcd-content",children:[s.jsx("div",{className:"lcd-product-name",children:t.name}),s.jsx("div",{className:"lcd-product-price",children:g(t.price)})]}):d?s.jsxs("div",{className:"lcd-total-content",children:[s.jsx("div",{className:"lcd-total-label",children:b("invoiceTotal","إجمالي الفاتورة")}),s.jsx("div",{className:"lcd-total-amount",children:g(d.total)}),s.jsxs("div",{className:"lcd-total-items",children:[d.itemCount," ",b("items","عناصر")]})]}):s.jsxs("div",{className:"lcd-placeholder",children:[s.jsx("div",{className:"lcd-welcome",children:b("welcomeMessage","مرحباً بك في نظام iCalDZ")}),s.jsx("div",{className:"lcd-instruction",children:b("scanToStart","امسح الباركود للبدء")})]})})})]})]})}),s.jsxs("div",{className:"dashboard-action-buttons",children:[s.jsxs("button",{className:"dashboard-btn dashboard-btn-sales",onClick:()=>x("sales"),children:[s.jsx("div",{className:"btn-icon",children:"🛒"}),s.jsxs("div",{className:"btn-content",children:[s.jsx("div",{className:"btn-title",children:b("salesInvoices","فواتير المبيعات")}),s.jsx("div",{className:"btn-subtitle",children:b("manageSalesInvoices","إدارة فواتير المبيعات")})]}),s.jsx("div",{className:"btn-shortcut",children:"F1"})]}),s.jsxs("button",{className:"dashboard-btn dashboard-btn-purchases",onClick:()=>x("purchases"),children:[s.jsx("div",{className:"btn-icon",children:"📦"}),s.jsxs("div",{className:"btn-content",children:[s.jsx("div",{className:"btn-title",children:b("purchaseInvoices","فواتير المشتريات")}),s.jsx("div",{className:"btn-subtitle",children:b("managePurchaseInvoices","إدارة فواتير المشتريات")})]}),s.jsx("div",{className:"btn-shortcut",children:"F2"})]}),s.jsxs("button",{className:"dashboard-btn dashboard-btn-inventory",onClick:()=>x("inventory"),children:[s.jsx("div",{className:"btn-icon",children:"📋"}),s.jsxs("div",{className:"btn-content",children:[s.jsx("div",{className:"btn-title",children:b("inventory","المخزون")}),s.jsx("div",{className:"btn-subtitle",children:b("manageInventory","إدارة المخزون والمنتجات")})]}),s.jsx("div",{className:"btn-shortcut",children:"F3"})]})]}),s.jsx("div",{className:"dashboard-stats",children:s.jsxs("div",{className:"stats-grid",children:[s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon",children:"💰"}),s.jsxs("div",{className:"stat-content",children:[s.jsx("h3",{children:b("totalSales","إجمالي المبيعات")}),s.jsx("div",{className:"stat-value",children:g(w.totalSales)})]})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon",children:"📄"}),s.jsxs("div",{className:"stat-content",children:[s.jsx("h3",{children:b("totalInvoices","عدد الفواتير")}),s.jsx("div",{className:"stat-value",children:w.totalInvoices})]})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon",children:"📦"}),s.jsxs("div",{className:"stat-content",children:[s.jsx("h3",{children:b("totalProducts","إجمالي المنتجات")}),s.jsx("div",{className:"stat-value",children:w.totalProducts})]})]}),s.jsxs("div",{className:"stat-card",children:[s.jsx("div",{className:"stat-icon",children:"⚠️"}),s.jsxs("div",{className:"stat-content",children:[s.jsx("h3",{children:b("lowStockProducts","منتجات قليلة المخزون")}),s.jsx("div",{className:"stat-value",children:w.lowStockProducts})]})]})]})}),s.jsxs("div",{className:"dashboard-recent",children:[s.jsx("h3",{children:b("recentInvoicesOperations","العمليات والفواتير الأخيرة")}),s.jsx("div",{className:"recent-invoices-table",children:s.jsxs("table",{children:[s.jsx("thead",{children:s.jsxs("tr",{children:[s.jsx("th",{children:b("invoiceNumber","رقم الفاتورة")}),s.jsx("th",{children:b("customer","العميل")}),s.jsx("th",{children:b("date","التاريخ")}),s.jsx("th",{children:b("total","الإجمالي")}),s.jsx("th",{children:b("paymentMethod","طريقة الدفع")})]})}),s.jsx("tbody",{children:p.slice(-5).reverse().map((e=>s.jsxs("tr",{children:[s.jsx("td",{children:e.invoiceNumber}),s.jsx("td",{children:e.customerName}),s.jsx("td",{children:new Date(e.date).toLocaleDateString("ar-DZ")}),s.jsx("td",{children:g(e.finalTotal)}),s.jsx("td",{children:e.paymentMethod})]},e.id)))})]})})]})]})};export{e as D};
