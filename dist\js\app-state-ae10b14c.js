import{r as e,j as t}from"./react-vendor-03c8a839.js";const s=e.createContext(),a=()=>{const t=e.useContext(s);if(!t)throw new Error("useAppState must be used within an AppStateProvider");return t},r=({children:a})=>{const[r,l]=e.useState("dashboard"),[o,u]=e.useState(!1),[S,i]=e.useState([]),[c,n]=e.useState([]),[d,p]=e.useState([]),[h,m]=e.useState([]),[g,I]=e.useState([]),[v,y]=e.useState([]),[P,N]=e.useState([]),[b,w]=e.useState([]),[F,C]=e.useState([]),[D,M]=e.useState(!1),[f,z]=e.useState(!1),[E,J]=e.useState(!1),[O,x]=e.useState(!1),[T,L]=e.useState(!1),[A,R]=e.useState(!1),[j,U]=e.useState(!1),[Z,k]=e.useState(!1),[q,B]=e.useState({invoiceNumber:"",date:"",customerId:"",customerName:"",paymentMethod:"نقداً",items:[],total:0,discount:0,tax:0,finalTotal:0}),[G,H]=e.useState({invoiceNumber:"",date:"",supplierId:"",supplierName:"",paymentMethod:"نقداً",items:[],total:0,discount:0,tax:0,finalTotal:0}),[K,Q]=e.useState({id:"",name:"",barcode:"",category:"",buyPrice:0,sellPrice:0,quantity:0,minStock:5,supplier:"",description:""}),[V,W]=e.useState({id:"",name:"",phone:"",email:"",address:"",balance:0}),[X,Y]=e.useState({id:"",name:"",phone:"",email:"",address:"",balance:0}),[$,_]=e.useState(""),[ee,te]=e.useState(""),[se,ae]=e.useState(""),[re,le]=e.useState(null),[oe,ue]=e.useState(null),[Se,ie]=e.useState(null),[ce,ne]=e.useState(null),[de,pe]=e.useState(null),[he,me]=e.useState(null),[ge,Ie]=e.useState([]),[ve,ye]=e.useState([]),[Pe,Ne]=e.useState([]),[be,we]=e.useState([]),[Fe,Ce]=e.useState([]),[De,Me]=e.useState([]),[fe,ze]=e.useState(""),[Ee,Je]=e.useState(""),[Oe,xe]=e.useState(""),[Te,Le]=e.useState(""),[Ae,Re]=e.useState(""),[je,Ue]=e.useState(""),[Ze,ke]=e.useState(""),[qe,Be]=e.useState(""),[Ge,He]=e.useState(""),[Ke,Qe]=e.useState(""),[Ve,We]=e.useState(""),[Xe,Ye]=e.useState(""),[$e,_e]=e.useState(""),[et,tt]=e.useState(""),[st,at]=e.useState(""),[rt,lt]=e.useState(""),[ot,ut]=e.useState({storeName:"iCalDZ Store",storeNumber:"ST001",storePhone:"0551930589",storeAddress:"Algeria",currency:"DZD",taxRate:0,logo:null}),[St,it]=e.useState({role:"مدير",name:"المدير"}),[ct,nt]=e.useState(!0),[dt,pt]=e.useState(!0),[ht,mt]=e.useState(!0),[gt,It]=e.useState(!0),[vt,yt]=e.useState(!1),[Pt,Nt]=e.useState([]);e.useEffect((()=>{bt()}),[]);const bt=()=>{try{const e=localStorage.getItem("icaldz-products");e&&i(JSON.parse(e));const t=localStorage.getItem("icaldz-customers");t&&n(JSON.parse(t));const s=localStorage.getItem("icaldz-suppliers");s&&p(JSON.parse(s));const a=localStorage.getItem("icaldz-invoices");a&&m(JSON.parse(a));const r=localStorage.getItem("icaldz-purchases");r&&I(JSON.parse(r));const l=localStorage.getItem("icaldz-repairs");l&&y(JSON.parse(l));const o=localStorage.getItem("icaldz-categories");o&&w(JSON.parse(o));const u=localStorage.getItem("icaldz-store-settings");u&&ut(JSON.parse(u));const S=localStorage.getItem("icaldz-expenses");S&&N(JSON.parse(S))}catch(e){console.error("Error loading data from storage:",e)}},wt={currentPage:r,setCurrentPage:l,isLoggedIn:o,setIsLoggedIn:u,products:S,setProducts:i,customers:c,setCustomers:n,suppliers:d,setSuppliers:p,savedInvoices:h,setSavedInvoices:m,savedPurchases:g,setSavedPurchases:I,savedRepairs:v,setSavedRepairs:y,expenses:P,setExpenses:N,categories:b,setCategories:w,sellers:F,setSellers:C,showSalesModal:D,setShowSalesModal:M,showPurchaseModal:f,setShowPurchaseModal:z,showProductModal:E,setShowProductModal:J,showCustomerModal:O,setShowCustomerModal:x,showSupplierModal:T,setShowSupplierModal:L,showCategoryModal:A,setShowCategoryModal:R,showSettingsModal:j,setShowSettingsModal:U,showExpensesModal:Z,setShowExpensesModal:k,salesInvoice:q,setSalesInvoice:B,purchaseInvoice:G,setPurchaseInvoice:H,newProduct:K,setNewProduct:Q,newCustomer:V,setNewCustomer:W,newSupplier:X,setNewSupplier:Y,dashboardScannerInput:$,setDashboardScannerInput:_,salesScannerInput:ee,setSalesScannerInput:te,editScannerInput:se,setEditScannerInput:ae,dashboardLcdDisplay:re,setDashboardLcdDisplay:le,salesLcdDisplay:oe,setSalesLcdDisplay:ue,editLcdDisplay:Se,setEditLcdDisplay:ie,dashboardTotalDisplay:ce,setDashboardTotalDisplay:ne,salesTotalDisplay:de,setSalesTotalDisplay:pe,editTotalDisplay:he,setEditTotalDisplay:me,selectedSalesInvoices:ge,setSelectedSalesInvoices:Ie,selectedPurchases:ve,setSelectedPurchases:ye,selectedCustomers:Pe,setSelectedCustomers:Ne,selectedProducts:be,setSelectedProducts:we,selectedSuppliers:Fe,setSelectedSuppliers:Ce,selectedSellers:De,setSelectedSellers:Me,productSearch:fe,setProductSearch:ze,selectedCategory:Ee,setSelectedCategory:Je,selectedStatus:Oe,setSelectedStatus:xe,customerFilter:Te,setCustomerFilter:Le,salesInvoiceFilter:Ae,setSalesInvoiceFilter:Re,purchaseInvoiceFilter:je,setPurchaseInvoiceFilter:Ue,salesInvoiceNumberFilter:Ze,setSalesInvoiceNumberFilter:ke,salesCustomerFilter:qe,setSalesCustomerFilter:Be,salesDateFilter:Ge,setSalesDateFilter:He,salesPaymentMethodFilter:Ke,setSalesPaymentMethodFilter:Qe,salesStatusFilter:Ve,setSalesStatusFilter:We,purchaseInvoiceNumberFilter:Xe,setPurchaseInvoiceNumberFilter:Ye,purchaseSupplierFilter:$e,setPurchaseSupplierFilter:_e,purchaseDateFilter:et,setPurchaseDateFilter:tt,purchasePaymentMethodFilter:st,setPurchasePaymentMethodFilter:at,purchaseStatusFilter:rt,setPurchaseStatusFilter:lt,storeSettings:ot,setStoreSettings:ut,currentUser:St,setCurrentUser:it,soundEnabled:ct,setSoundEnabled:nt,shortcutsEnabled:dt,setShortcutsEnabled:pt,printerEnabled:ht,setPrinterEnabled:mt,notificationsEnabled:gt,setNotificationsEnabled:It,showScrollTop:vt,setShowScrollTop:yt,toasts:Pt,setToasts:Nt,saveProducts:e=>{localStorage.setItem("icaldz-products",JSON.stringify(e)),i(e)},saveCustomers:e=>{localStorage.setItem("icaldz-customers",JSON.stringify(e)),n(e)},saveSuppliers:e=>{localStorage.setItem("icaldz-suppliers",JSON.stringify(e)),p(e)},saveInvoices:e=>{localStorage.setItem("icaldz-invoices",JSON.stringify(e)),m(e)},savePurchases:e=>{localStorage.setItem("icaldz-purchases",JSON.stringify(e)),I(e)},saveRepairs:e=>{localStorage.setItem("icaldz-repairs",JSON.stringify(e)),y(e)},saveCategories:e=>{localStorage.setItem("icaldz-categories",JSON.stringify(e)),w(e)},saveStoreSettings:e=>{localStorage.setItem("icaldz-store-settings",JSON.stringify(e)),ut(e)},saveExpenses:e=>{localStorage.setItem("icaldz-expenses",JSON.stringify(e)),N(e)}};return t.jsx(s.Provider,{value:wt,children:a})};export{r as A,a as u};
