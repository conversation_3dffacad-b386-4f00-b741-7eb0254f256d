// Report generation functions extracted from App.jsx to reduce file size

export const generateSalesReport = (savedInvoices, currentLanguage, t, formatPrice) => {
  // Get current language direction and language code
  const isRTL = currentLanguage === 'ar';
  const langCode = currentLanguage === 'ar' ? 'ar' : currentLanguage === 'fr' ? 'fr' : 'en';

  // Calculate totals
  const totalSales = savedInvoices.reduce((sum, inv) => sum + inv.finalTotal, 0);
  const totalInvoices = savedInvoices.length;
  const cashSales = savedInvoices.filter(inv => inv.paymentMethod === 'نقداً' || inv.paymentMethod === 'Espèces' || inv.paymentMethod === 'En espèces' || inv.paymentMethod === 'Cash').reduce((sum, inv) => sum + inv.finalTotal, 0);
  const creditSales = savedInvoices.filter(inv => inv.paymentMethod === 'دين' || inv.paymentMethod === 'Crédit' || inv.paymentMethod === 'Credit').reduce((sum, inv) => sum + inv.finalTotal, 0);

  // Generate report content
  const reportContent = `
    <div style="font-family: Arial, sans-serif; direction: ${isRTL ? 'rtl' : 'ltr'}; text-align: ${isRTL ? 'right' : 'left'};">
      <h1 style="text-align: center; color: #333; margin-bottom: 30px;">
        ${t('salesReport', 'تقرير المبيعات')}
      </h1>
      
      <div style="margin-bottom: 20px;">
        <h3>${t('summary', 'الملخص')}</h3>
        <p><strong>${t('totalSales', 'إجمالي المبيعات')}:</strong> ${formatPrice(totalSales)}</p>
        <p><strong>${t('totalInvoices', 'عدد الفواتير')}:</strong> ${totalInvoices}</p>
        <p><strong>${t('cashSales', 'المبيعات النقدية')}:</strong> ${formatPrice(cashSales)}</p>
        <p><strong>${t('creditSales', 'المبيعات الآجلة')}:</strong> ${formatPrice(creditSales)}</p>
      </div>

      <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
        <thead>
          <tr style="background-color: #f5f5f5;">
            <th style="border: 1px solid #ddd; padding: 8px;">${t('invoiceNumber', 'رقم الفاتورة')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('customer', 'العميل')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('date', 'التاريخ')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('total', 'الإجمالي')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('paymentMethod', 'طريقة الدفع')}</th>
          </tr>
        </thead>
        <tbody>
          ${savedInvoices.map(invoice => `
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;">${invoice.invoiceNumber}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${invoice.customerName}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${new Date(invoice.date).toLocaleDateString(langCode)}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${formatPrice(invoice.finalTotal)}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${invoice.paymentMethod}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  // Open print window
  const printWindow = window.open('', '_blank', 'width=800,height=600');
  printWindow.document.write(`
    <html>
      <head>
        <title>${t('salesReport', 'تقرير المبيعات')}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        ${reportContent}
        <script>
          window.onload = function() {
            window.print();
          }
        </script>
      </body>
    </html>
  `);
  printWindow.document.close();
};

export const generatePurchasesReport = (savedPurchases, currentLanguage, t, formatPrice) => {
  const isRTL = currentLanguage === 'ar';
  const langCode = currentLanguage === 'ar' ? 'ar' : currentLanguage === 'fr' ? 'fr' : 'en';

  const totalPurchases = savedPurchases.reduce((sum, pur) => sum + pur.finalTotal, 0);
  const totalInvoices = savedPurchases.length;

  const reportContent = `
    <div style="font-family: Arial, sans-serif; direction: ${isRTL ? 'rtl' : 'ltr'}; text-align: ${isRTL ? 'right' : 'left'};">
      <h1 style="text-align: center; color: #333; margin-bottom: 30px;">
        ${t('purchasesReport', 'تقرير المشتريات')}
      </h1>
      
      <div style="margin-bottom: 20px;">
        <h3>${t('summary', 'الملخص')}</h3>
        <p><strong>${t('totalPurchases', 'إجمالي المشتريات')}:</strong> ${formatPrice(totalPurchases)}</p>
        <p><strong>${t('totalInvoices', 'عدد الفواتير')}:</strong> ${totalInvoices}</p>
      </div>

      <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
        <thead>
          <tr style="background-color: #f5f5f5;">
            <th style="border: 1px solid #ddd; padding: 8px;">${t('invoiceNumber', 'رقم الفاتورة')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('supplier', 'المورد')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('date', 'التاريخ')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('total', 'الإجمالي')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('paymentMethod', 'طريقة الدفع')}</th>
          </tr>
        </thead>
        <tbody>
          ${savedPurchases.map(purchase => `
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;">${purchase.invoiceNumber}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${purchase.supplierName}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${new Date(purchase.date).toLocaleDateString(langCode)}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${formatPrice(purchase.finalTotal)}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${purchase.paymentMethod}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  const printWindow = window.open('', '_blank', 'width=800,height=600');
  printWindow.document.write(`
    <html>
      <head>
        <title>${t('purchasesReport', 'تقرير المشتريات')}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        ${reportContent}
        <script>
          window.onload = function() {
            window.print();
          }
        </script>
      </body>
    </html>
  `);
  printWindow.document.close();
};

export const generateInventoryReport = (products, currentLanguage, t, formatPrice) => {
  const isRTL = currentLanguage === 'ar';
  const langCode = currentLanguage === 'ar' ? 'ar' : currentLanguage === 'fr' ? 'fr' : 'en';

  const totalProducts = products.length;
  const totalValue = products.reduce((sum, product) => sum + (product.stock * product.buyPrice), 0);
  const lowStockProducts = products.filter(p => p.stock <= p.minStock).length;

  const reportContent = `
    <div style="font-family: Arial, sans-serif; direction: ${isRTL ? 'rtl' : 'ltr'}; text-align: ${isRTL ? 'right' : 'left'};">
      <h1 style="text-align: center; color: #333; margin-bottom: 30px;">
        ${t('inventoryReport', 'تقرير المخزون')}
      </h1>
      
      <div style="margin-bottom: 20px;">
        <h3>${t('summary', 'الملخص')}</h3>
        <p><strong>${t('totalProducts', 'إجمالي المنتجات')}:</strong> ${totalProducts}</p>
        <p><strong>${t('totalInventoryValue', 'قيمة المخزون الإجمالية')}:</strong> ${formatPrice(totalValue)}</p>
        <p><strong>${t('lowStockProducts', 'منتجات قليلة المخزون')}:</strong> ${lowStockProducts}</p>
      </div>

      <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
        <thead>
          <tr style="background-color: #f5f5f5;">
            <th style="border: 1px solid #ddd; padding: 8px;">${t('name', 'الاسم')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('category', 'الفئة')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('stock', 'المخزون')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('buyPrice', 'سعر الشراء')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('sellPrice', 'سعر البيع')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('value', 'القيمة')}</th>
          </tr>
        </thead>
        <tbody>
          ${products.map(product => `
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;">${product.name}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${product.category}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${product.stock}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${formatPrice(product.buyPrice)}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${formatPrice(product.sellPrice)}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${formatPrice(product.stock * product.buyPrice)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  const printWindow = window.open('', '_blank', 'width=800,height=600');
  printWindow.document.write(`
    <html>
      <head>
        <title>${t('inventoryReport', 'تقرير المخزون')}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        ${reportContent}
        <script>
          window.onload = function() {
            window.print();
          }
        </script>
      </body>
    </html>
  `);
  printWindow.document.close();
};

export const generateCustomersReport = (customers, savedInvoices, currentLanguage, t, formatPrice) => {
  const isRTL = currentLanguage === 'ar';
  const langCode = currentLanguage === 'ar' ? 'ar' : currentLanguage === 'fr' ? 'fr' : 'en';

  const totalCustomers = customers.length;
  const totalDebt = customers.reduce((sum, customer) => sum + (customer.balance || 0), 0);

  const reportContent = `
    <div style="font-family: Arial, sans-serif; direction: ${isRTL ? 'rtl' : 'ltr'}; text-align: ${isRTL ? 'right' : 'left'};">
      <h1 style="text-align: center; color: #333; margin-bottom: 30px;">
        ${t('customersReport', 'تقرير العملاء')}
      </h1>
      
      <div style="margin-bottom: 20px;">
        <h3>${t('summary', 'الملخص')}</h3>
        <p><strong>${t('totalCustomers', 'إجمالي العملاء')}:</strong> ${totalCustomers}</p>
        <p><strong>${t('totalDebt', 'إجمالي الديون')}:</strong> ${formatPrice(totalDebt)}</p>
      </div>

      <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
        <thead>
          <tr style="background-color: #f5f5f5;">
            <th style="border: 1px solid #ddd; padding: 8px;">${t('name', 'الاسم')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('phone', 'الهاتف')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('email', 'البريد الإلكتروني')}</th>
            <th style="border: 1px solid #ddd; padding: 8px;">${t('balance', 'الرصيد')}</th>
          </tr>
        </thead>
        <tbody>
          ${customers.map(customer => `
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;">${customer.name}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${customer.phone || '-'}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${customer.email || '-'}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${formatPrice(customer.balance || 0)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;

  const printWindow = window.open('', '_blank', 'width=800,height=600');
  printWindow.document.write(`
    <html>
      <head>
        <title>${t('customersReport', 'تقرير العملاء')}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        ${reportContent}
        <script>
          window.onload = function() {
            window.print();
          }
        </script>
      </body>
    </html>
  `);
  printWindow.document.close();
};
