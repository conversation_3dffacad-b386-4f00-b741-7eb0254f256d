import React, { createContext, useContext, useState, useEffect } from 'react';

const AppStateContext = createContext();

export const useAppState = () => {
  const context = useContext(AppStateContext);
  if (!context) {
    throw new Error('useAppState must be used within an AppStateProvider');
  }
  return context;
};

export const AppStateProvider = ({ children }) => {
  // Navigation state
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // Core data states
  const [products, setProducts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [savedInvoices, setSavedInvoices] = useState([]);
  const [savedPurchases, setSavedPurchases] = useState([]);
  const [savedRepairs, setSavedRepairs] = useState([]);
  const [expenses, setExpenses] = useState([]);
  const [categories, setCategories] = useState([]);
  const [sellers, setSellers] = useState([]);

  // Modal states
  const [showSalesModal, setShowSalesModal] = useState(false);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [showProductModal, setShowProductModal] = useState(false);
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [showSupplierModal, setShowSupplierModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showExpensesModal, setShowExpensesModal] = useState(false);

  // Form states
  const [salesInvoice, setSalesInvoice] = useState({
    invoiceNumber: '',
    date: '',
    customerId: '',
    customerName: '',
    paymentMethod: 'نقداً',
    items: [],
    total: 0,
    discount: 0,
    tax: 0,
    finalTotal: 0
  });

  const [purchaseInvoice, setPurchaseInvoice] = useState({
    invoiceNumber: '',
    date: '',
    supplierId: '',
    supplierName: '',
    paymentMethod: 'نقداً',
    items: [],
    total: 0,
    discount: 0,
    tax: 0,
    finalTotal: 0
  });

  const [newProduct, setNewProduct] = useState({
    id: '',
    name: '',
    barcode: '',
    category: '',
    buyPrice: 0,
    sellPrice: 0,
    quantity: 0,
    minStock: 5,
    supplier: '',
    description: ''
  });

  const [newCustomer, setNewCustomer] = useState({
    id: '',
    name: '',
    phone: '',
    email: '',
    address: '',
    balance: 0
  });

  const [newSupplier, setNewSupplier] = useState({
    id: '',
    name: '',
    phone: '',
    email: '',
    address: '',
    balance: 0
  });

  // Scanner states
  const [dashboardScannerInput, setDashboardScannerInput] = useState('');
  const [salesScannerInput, setSalesScannerInput] = useState('');
  const [editScannerInput, setEditScannerInput] = useState('');

  // LCD Display states
  const [dashboardLcdDisplay, setDashboardLcdDisplay] = useState(null);
  const [salesLcdDisplay, setSalesLcdDisplay] = useState(null);
  const [editLcdDisplay, setEditLcdDisplay] = useState(null);
  const [dashboardTotalDisplay, setDashboardTotalDisplay] = useState(null);
  const [salesTotalDisplay, setSalesTotalDisplay] = useState(null);
  const [editTotalDisplay, setEditTotalDisplay] = useState(null);

  // Selection states for bulk operations
  const [selectedSalesInvoices, setSelectedSalesInvoices] = useState([]);
  const [selectedPurchases, setSelectedPurchases] = useState([]);
  const [selectedCustomers, setSelectedCustomers] = useState([]);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [selectedSuppliers, setSelectedSuppliers] = useState([]);
  const [selectedSellers, setSelectedSellers] = useState([]);

  // Filter states
  const [productSearch, setProductSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [customerFilter, setCustomerFilter] = useState('');
  const [salesInvoiceFilter, setSalesInvoiceFilter] = useState('');
  const [purchaseInvoiceFilter, setPurchaseInvoiceFilter] = useState('');

  // Sales Management Filter States
  const [salesInvoiceNumberFilter, setSalesInvoiceNumberFilter] = useState('');
  const [salesCustomerFilter, setSalesCustomerFilter] = useState('');
  const [salesDateFilter, setSalesDateFilter] = useState('');
  const [salesPaymentMethodFilter, setSalesPaymentMethodFilter] = useState('');
  const [salesStatusFilter, setSalesStatusFilter] = useState('');

  // Purchase Management Filter States
  const [purchaseInvoiceNumberFilter, setPurchaseInvoiceNumberFilter] = useState('');
  const [purchaseSupplierFilter, setPurchaseSupplierFilter] = useState('');
  const [purchaseDateFilter, setPurchaseDateFilter] = useState('');
  const [purchasePaymentMethodFilter, setPurchasePaymentMethodFilter] = useState('');
  const [purchaseStatusFilter, setPurchaseStatusFilter] = useState('');

  // Settings and configuration
  const [storeSettings, setStoreSettings] = useState({
    storeName: 'iCalDZ Store',
    storeNumber: 'ST001',
    storePhone: '0551930589',
    storeAddress: 'Algeria',
    currency: 'DZD',
    taxRate: 0,
    logo: null
  });

  const [currentUser, setCurrentUser] = useState({ 
    role: 'مدير', 
    name: 'المدير' 
  });

  // System states
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [shortcutsEnabled, setShortcutsEnabled] = useState(true);
  const [printerEnabled, setPrinterEnabled] = useState(true);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [toasts, setToasts] = useState([]);

  // Load data from localStorage on mount
  useEffect(() => {
    loadDataFromStorage();
  }, []);

  const loadDataFromStorage = () => {
    try {
      // Load products
      const savedProducts = localStorage.getItem('icaldz-products');
      if (savedProducts) {
        setProducts(JSON.parse(savedProducts));
      }

      // Load customers
      const savedCustomers = localStorage.getItem('icaldz-customers');
      if (savedCustomers) {
        setCustomers(JSON.parse(savedCustomers));
      }

      // Load suppliers
      const savedSuppliers = localStorage.getItem('icaldz-suppliers');
      if (savedSuppliers) {
        setSuppliers(JSON.parse(savedSuppliers));
      }

      // Load invoices
      const savedInvoicesData = localStorage.getItem('icaldz-invoices');
      if (savedInvoicesData) {
        setSavedInvoices(JSON.parse(savedInvoicesData));
      }

      // Load purchases
      const savedPurchasesData = localStorage.getItem('icaldz-purchases');
      if (savedPurchasesData) {
        setSavedPurchases(JSON.parse(savedPurchasesData));
      }

      // Load repairs
      const savedRepairsData = localStorage.getItem('icaldz-repairs');
      if (savedRepairsData) {
        setSavedRepairs(JSON.parse(savedRepairsData));
      }

      // Load categories
      const savedCategories = localStorage.getItem('icaldz-categories');
      if (savedCategories) {
        setCategories(JSON.parse(savedCategories));
      }

      // Load store settings
      const savedSettings = localStorage.getItem('icaldz-store-settings');
      if (savedSettings) {
        setStoreSettings(JSON.parse(savedSettings));
      }

      // Load expenses
      const savedExpenses = localStorage.getItem('icaldz-expenses');
      if (savedExpenses) {
        setExpenses(JSON.parse(savedExpenses));
      }

    } catch (error) {
      console.error('Error loading data from storage:', error);
    }
  };

  // Save functions
  const saveProducts = (productsData) => {
    localStorage.setItem('icaldz-products', JSON.stringify(productsData));
    setProducts(productsData);
  };

  const saveCustomers = (customersData) => {
    localStorage.setItem('icaldz-customers', JSON.stringify(customersData));
    setCustomers(customersData);
  };

  const saveSuppliers = (suppliersData) => {
    localStorage.setItem('icaldz-suppliers', JSON.stringify(suppliersData));
    setSuppliers(suppliersData);
  };

  const saveInvoices = (invoicesData) => {
    localStorage.setItem('icaldz-invoices', JSON.stringify(invoicesData));
    setSavedInvoices(invoicesData);
  };

  const savePurchases = (purchasesData) => {
    localStorage.setItem('icaldz-purchases', JSON.stringify(purchasesData));
    setSavedPurchases(purchasesData);
  };

  const saveRepairs = (repairsData) => {
    localStorage.setItem('icaldz-repairs', JSON.stringify(repairsData));
    setSavedRepairs(repairsData);
  };

  const saveCategories = (categoriesData) => {
    localStorage.setItem('icaldz-categories', JSON.stringify(categoriesData));
    setCategories(categoriesData);
  };

  const saveStoreSettings = (settingsData) => {
    localStorage.setItem('icaldz-store-settings', JSON.stringify(settingsData));
    setStoreSettings(settingsData);
  };

  const saveExpenses = (expensesData) => {
    localStorage.setItem('icaldz-expenses', JSON.stringify(expensesData));
    setExpenses(expensesData);
  };

  const value = {
    // Navigation
    currentPage,
    setCurrentPage,
    isLoggedIn,
    setIsLoggedIn,

    // Core data
    products,
    setProducts,
    customers,
    setCustomers,
    suppliers,
    setSuppliers,
    savedInvoices,
    setSavedInvoices,
    savedPurchases,
    setSavedPurchases,
    savedRepairs,
    setSavedRepairs,
    expenses,
    setExpenses,
    categories,
    setCategories,
    sellers,
    setSellers,

    // Modals
    showSalesModal,
    setShowSalesModal,
    showPurchaseModal,
    setShowPurchaseModal,
    showProductModal,
    setShowProductModal,
    showCustomerModal,
    setShowCustomerModal,
    showSupplierModal,
    setShowSupplierModal,
    showCategoryModal,
    setShowCategoryModal,
    showSettingsModal,
    setShowSettingsModal,
    showExpensesModal,
    setShowExpensesModal,

    // Forms
    salesInvoice,
    setSalesInvoice,
    purchaseInvoice,
    setPurchaseInvoice,
    newProduct,
    setNewProduct,
    newCustomer,
    setNewCustomer,
    newSupplier,
    setNewSupplier,

    // Scanners
    dashboardScannerInput,
    setDashboardScannerInput,
    salesScannerInput,
    setSalesScannerInput,
    editScannerInput,
    setEditScannerInput,

    // LCD Displays
    dashboardLcdDisplay,
    setDashboardLcdDisplay,
    salesLcdDisplay,
    setSalesLcdDisplay,
    editLcdDisplay,
    setEditLcdDisplay,
    dashboardTotalDisplay,
    setDashboardTotalDisplay,
    salesTotalDisplay,
    setSalesTotalDisplay,
    editTotalDisplay,
    setEditTotalDisplay,

    // Selections
    selectedSalesInvoices,
    setSelectedSalesInvoices,
    selectedPurchases,
    setSelectedPurchases,
    selectedCustomers,
    setSelectedCustomers,
    selectedProducts,
    setSelectedProducts,
    selectedSuppliers,
    setSelectedSuppliers,
    selectedSellers,
    setSelectedSellers,

    // Filters
    productSearch,
    setProductSearch,
    selectedCategory,
    setSelectedCategory,
    selectedStatus,
    setSelectedStatus,
    customerFilter,
    setCustomerFilter,
    salesInvoiceFilter,
    setSalesInvoiceFilter,
    purchaseInvoiceFilter,
    setPurchaseInvoiceFilter,
    salesInvoiceNumberFilter,
    setSalesInvoiceNumberFilter,
    salesCustomerFilter,
    setSalesCustomerFilter,
    salesDateFilter,
    setSalesDateFilter,
    salesPaymentMethodFilter,
    setSalesPaymentMethodFilter,
    salesStatusFilter,
    setSalesStatusFilter,
    purchaseInvoiceNumberFilter,
    setPurchaseInvoiceNumberFilter,
    purchaseSupplierFilter,
    setPurchaseSupplierFilter,
    purchaseDateFilter,
    setPurchaseDateFilter,
    purchasePaymentMethodFilter,
    setPurchasePaymentMethodFilter,
    purchaseStatusFilter,
    setPurchaseStatusFilter,

    // Settings
    storeSettings,
    setStoreSettings,
    currentUser,
    setCurrentUser,
    soundEnabled,
    setSoundEnabled,
    shortcutsEnabled,
    setShortcutsEnabled,
    printerEnabled,
    setPrinterEnabled,
    notificationsEnabled,
    setNotificationsEnabled,
    showScrollTop,
    setShowScrollTop,
    toasts,
    setToasts,

    // Save functions
    saveProducts,
    saveCustomers,
    saveSuppliers,
    saveInvoices,
    savePurchases,
    saveRepairs,
    saveCategories,
    saveStoreSettings,
    saveExpenses
  };

  return (
    <AppStateContext.Provider value={value}>
      {children}
    </AppStateContext.Provider>
  );
};
