import React, { useState, useEffect } from 'react';
import './index.css';

// Import contexts and providers
import { LanguageProvider, useLanguage } from './LanguageContext.jsx';
import { AppStateProvider } from './contexts/AppStateContext.jsx';

// Import main components
import AppRouter from './components/AppRouter.jsx';
import ActivationDialog from './ActivationDialog.jsx';
import LanguageSelectionDialog from './LanguageSelectionDialog.jsx';

// Import utility managers
import { activationManager } from './activation.js';
import { SoundManager } from './SoundManager.js';
import { memoryManager } from './MemoryManager.js';

// Main App Content Component
function AppContent() {
  const { t, isLanguageSelected, setIsLanguageSelected, currentLanguage } = useLanguage();

  // Activation state
  const [isActivated, setIsActivated] = useState(false);
  const [activationChecked, setActivationChecked] = useState(false);

  // Login state
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loginFormData, setLoginFormData] = useState({ username: '', password: '' });

  // System settings
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [shortcutsEnabled, setShortcutsEnabled] = useState(true);
  const [printerEnabled, setPrinterEnabled] = useState(true);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  // Check activation status on app start
  useEffect(() => {
    const checkActivation = async () => {
      try {
        const activationStatus = await activationManager.checkActivation();
        setIsActivated(activationStatus.isActivated);
        setActivationChecked(true);
        
        if (activationStatus.isActivated) {
          console.log('✅ Application is activated');
          // Check login status
          const loginStatus = checkLoginStatus();
          setIsLoggedIn(loginStatus.isLoggedIn);
        } else {
          console.log('❌ Application needs activation');
        }
      } catch (error) {
        console.error('Error checking activation:', error);
        setActivationChecked(true);
      }
    };

    checkActivation();
  }, []);

  // Initialize system managers
  useEffect(() => {
    // Initialize sound manager
    if (soundEnabled) {
      SoundManager.initialize();
    }

    // Initialize memory manager
    if (memoryManager) {
      memoryManager.startMonitoring();
    }

    // Initialize barcode shortcut manager
    if (!window.barcodeShortcutManager) {
      window.barcodeShortcutManager = {
        isEnabled: true,
        isBarcodeActive: false,
        setShortcutsEnabled: (enabled) => {
          window.barcodeShortcutManager.isEnabled = enabled;
        },
        checkBarcodeInput: (target) => {
          if (!target || target.tagName !== 'INPUT') return false;
          
          const barcodeIndicators = [
            'barcode-input', 'scanner-input', 'dashboard-scanner',
            'sales-scanner', 'edit-scanner', 'product-barcode'
          ];

          // Check class names
          if (target.className) {
            const classNames = target.className.toLowerCase();
            if (barcodeIndicators.some(indicator => classNames.includes(indicator))) {
              return true;
            }
          }

          // Check element ID
          if (target.id) {
            const id = target.id.toLowerCase();
            if (barcodeIndicators.some(indicator => id.includes(indicator))) {
              return true;
            }
          }

          // Check placeholder text
          if (target.placeholder) {
            const placeholder = target.placeholder.toLowerCase();
            if (placeholder.includes('barcode') || placeholder.includes('باركود') ||
                placeholder.includes('scanner') || placeholder.includes('مسح')) {
              return true;
            }
          }

          return false;
        }
      };
    }

    // Cleanup function
    return () => {
      if (memoryManager) {
        memoryManager.stopMonitoring();
      }
    };
  }, [soundEnabled]);

  // Check login status from localStorage
  const checkLoginStatus = () => {
    try {
      const savedLogin = localStorage.getItem('icaldz-login-status');
      if (savedLogin) {
        const loginData = JSON.parse(savedLogin);
        const now = Date.now();
        
        // Check if login is still valid (24 hours)
        if (loginData.timestamp && (now - loginData.timestamp) < 24 * 60 * 60 * 1000) {
          return {
            isLoggedIn: true,
            page: loginData.currentPage || 'dashboard'
          };
        }
      }
    } catch (error) {
      console.error('Error checking login status:', error);
    }
    
    return { isLoggedIn: false, page: 'login' };
  };

  // Handle login
  const handleLogin = (e) => {
    e.preventDefault();
    
    const { username, password } = loginFormData;
    
    // Simple authentication (in production, this should be more secure)
    const validCredentials = [
      { username: 'admin', password: 'admin123' },
      { username: 'مدير', password: '123456' },
      { username: 'user', password: 'user123' }
    ];

    const isValid = validCredentials.some(cred => 
      cred.username === username && cred.password === password
    );

    if (isValid) {
      const loginData = {
        isLoggedIn: true,
        username: username,
        timestamp: Date.now(),
        currentPage: 'dashboard'
      };
      
      localStorage.setItem('icaldz-login-status', JSON.stringify(loginData));
      setIsLoggedIn(true);
      
      // Play success sound
      if (soundEnabled && SoundManager) {
        SoundManager.playSuccess();
      }
    } else {
      alert(t('invalidCredentials', 'اسم المستخدم أو كلمة المرور غير صحيحة'));
      
      // Play error sound
      if (soundEnabled && SoundManager) {
        SoundManager.playError();
      }
    }
  };

  // Handle logout
  const handleLogout = () => {
    localStorage.removeItem('icaldz-login-status');
    setIsLoggedIn(false);
    setLoginFormData({ username: '', password: '' });
  };

  // Handle activation success
  const handleActivationSuccess = () => {
    setIsActivated(true);
  };

  // Show language selection if not selected
  if (!isLanguageSelected) {
    return <LanguageSelectionDialog />;
  }

  // Show activation dialog if not activated
  if (activationChecked && !isActivated) {
    return <ActivationDialog onActivationSuccess={handleActivationSuccess} />;
  }

  // Show loading while checking activation
  if (!activationChecked) {
    return (
      <div className="loading-screen">
        <div className="loading-content">
          <div className="loading-spinner"></div>
          <h2>{t('checkingActivation', 'جاري التحقق من التفعيل...')}</h2>
        </div>
      </div>
    );
  }

  // Show login screen if not logged in
  if (!isLoggedIn) {
    return (
      <div className={`login-screen lang-${currentLanguage}`}>
        <div className="login-container">
          <div className="login-header">
            <h1>iCalDZ</h1>
            <p>{t('accountingSystem', 'نظام المحاسبة')}</p>
          </div>
          
          <form onSubmit={handleLogin} className="login-form">
            <div className="form-group">
              <label>{t('username', 'اسم المستخدم')}</label>
              <input
                type="text"
                value={loginFormData.username}
                onChange={(e) => setLoginFormData(prev => ({ ...prev, username: e.target.value }))}
                placeholder={t('enterUsername', 'أدخل اسم المستخدم')}
                required
              />
            </div>
            
            <div className="form-group">
              <label>{t('password', 'كلمة المرور')}</label>
              <input
                type="password"
                value={loginFormData.password}
                onChange={(e) => setLoginFormData(prev => ({ ...prev, password: e.target.value }))}
                placeholder={t('enterPassword', 'أدخل كلمة المرور')}
                required
              />
            </div>
            
            <button type="submit" className="login-btn">
              {t('login', 'تسجيل الدخول')}
            </button>
          </form>
          
          <div className="login-footer">
            <p>{t('defaultCredentials', 'المستخدم الافتراضي: admin / admin123')}</p>
          </div>
        </div>
      </div>
    );
  }

  // Main application content
  return (
    <AppStateProvider>
      <AppRouter />
    </AppStateProvider>
  );
}

// Main App Wrapper with Language Provider
function App() {
  return (
    <LanguageProvider>
      <AppContent />
    </LanguageProvider>
  );
}

export default App;
