import React, { useState, useRef, useEffect } from 'react';
import { useLanguage } from '../LanguageContext.jsx';
import { useAppState } from '../contexts/AppStateContext.jsx';

// Import page components
import Dashboard from './pages/Dashboard.jsx';
import SalesManagement from './pages/SalesManagement.jsx';
import InventoryManagement from './pages/InventoryManagement.jsx';
import CustomerPage from './CustomerPage.jsx';
import RepairPage from './RepairPage.jsx';

// Import utility functions and managers
import { SoundManager } from '../SoundManager.js';
import { thermalPrinter } from '../ThermalPrinter.js';
import { memoryManager } from '../MemoryManager.js';

const AppRouter = () => {
  const { t, currentLanguage } = useLanguage();
  const {
    currentPage,
    setCurrentPage,
    isLoggedIn,
    setIsLoggedIn,
    products,
    setProducts,
    customers,
    setCustomers,
    suppliers,
    setSuppliers,
    savedInvoices,
    setSavedInvoices,
    savedPurchases,
    setSavedPurchases,
    savedRepairs,
    setSavedRepairs,
    categories,
    setCategories,
    storeSettings,
    setStoreSettings,
    currentUser,
    setCurrentUser,
    soundEnabled,
    setSoundEnabled,
    shortcutsEnabled,
    setShortcutsEnabled,
    printerEnabled,
    setPrinterEnabled,
    notificationsEnabled,
    setNotificationsEnabled,
    showScrollTop,
    setShowScrollTop,
    toasts,
    setToasts,
    // Scanner states
    dashboardScannerInput,
    setDashboardScannerInput,
    salesScannerInput,
    setSalesScannerInput,
    editScannerInput,
    setEditScannerInput,
    // LCD states
    dashboardLcdDisplay,
    setDashboardLcdDisplay,
    salesLcdDisplay,
    setSalesLcdDisplay,
    editLcdDisplay,
    setEditLcdDisplay,
    dashboardTotalDisplay,
    setDashboardTotalDisplay,
    salesTotalDisplay,
    setSalesTotalDisplay,
    editTotalDisplay,
    setEditTotalDisplay,
    // Modal states
    showSalesModal,
    setShowSalesModal,
    showPurchaseModal,
    setShowPurchaseModal,
    showProductModal,
    setShowProductModal,
    showCustomerModal,
    setShowCustomerModal,
    showSupplierModal,
    setShowSupplierModal,
    showCategoryModal,
    setShowCategoryModal,
    showSettingsModal,
    setShowSettingsModal,
    // Form states
    salesInvoice,
    setSalesInvoice,
    purchaseInvoice,
    setPurchaseInvoice,
    newProduct,
    setNewProduct,
    newCustomer,
    setNewCustomer,
    newSupplier,
    setNewSupplier,
    // Selection states
    selectedSalesInvoices,
    setSelectedSalesInvoices,
    selectedPurchases,
    setSelectedPurchases,
    selectedCustomers,
    setSelectedCustomers,
    selectedProducts,
    setSelectedProducts,
    selectedSuppliers,
    setSelectedSuppliers,
    // Filter states
    productSearch,
    setProductSearch,
    selectedCategory,
    setSelectedCategory,
    selectedStatus,
    setSelectedStatus,
    customerFilter,
    setCustomerFilter,
    salesInvoiceFilter,
    setSalesInvoiceFilter,
    purchaseInvoiceFilter,
    setPurchaseInvoiceFilter,
    salesInvoiceNumberFilter,
    setSalesInvoiceNumberFilter,
    salesCustomerFilter,
    setSalesCustomerFilter,
    salesDateFilter,
    setSalesDateFilter,
    salesPaymentMethodFilter,
    setSalesPaymentMethodFilter,
    salesStatusFilter,
    setSalesStatusFilter,
    purchaseInvoiceNumberFilter,
    setPurchaseInvoiceNumberFilter,
    purchaseSupplierFilter,
    setPurchaseSupplierFilter,
    purchaseDateFilter,
    setPurchaseDateFilter,
    purchasePaymentMethodFilter,
    setPurchasePaymentMethodFilter,
    purchaseStatusFilter,
    setPurchaseStatusFilter,
    // Save functions
    saveProducts,
    saveCustomers,
    saveSuppliers,
    saveInvoices,
    savePurchases,
    saveRepairs,
    saveCategories,
    saveStoreSettings
  } = useAppState();

  // Refs for scanner inputs
  const dashboardScannerRef = useRef(null);
  const salesScannerRef = useRef(null);
  const editScannerRef = useRef(null);

  // Toast notification system
  const showToast = (message, type = 'info', duration = 3000) => {
    const id = Date.now();
    const toast = { id, message, type, duration };
    setToasts(prev => [...prev, toast]);

    setTimeout(() => {
      setToasts(prev => prev.filter(t => t.id !== id));
    }, duration);
  };

  // Format price function
  const formatPrice = (price) => {
    const numPrice = parseFloat(price) || 0;
    return new Intl.NumberFormat('ar-DZ', {
      style: 'currency',
      currency: storeSettings.currency || 'DZD',
      minimumFractionDigits: 2
    }).format(numPrice);
  };

  // Navigation function
  const navigateToPage = (page) => {
    setCurrentPage(page);
    
    // Save current page to localStorage
    localStorage.setItem('icaldz-current-page', page);
    
    // Clear any active scanner inputs when navigating
    setDashboardScannerInput('');
    setSalesScannerInput('');
    setEditScannerInput('');
    
    // Clear LCD displays
    setDashboardLcdDisplay(null);
    setSalesLcdDisplay(null);
    setEditLcdDisplay(null);
    setDashboardTotalDisplay(null);
    setSalesTotalDisplay(null);
    setEditTotalDisplay(null);

    // Focus appropriate scanner based on page
    setTimeout(() => {
      if (page === 'dashboard' && dashboardScannerRef.current) {
        dashboardScannerRef.current.focus();
      } else if (page === 'sales' && salesScannerRef.current) {
        salesScannerRef.current.focus();
      }
    }, 100);
  };

  // Calculate dashboard statistics
  const calculateDashboardStats = () => {
    const totalSales = savedInvoices.reduce((sum, invoice) => sum + invoice.finalTotal, 0);
    const totalInvoices = savedInvoices.length;
    const totalProducts = products.length;
    const lowStockProducts = products.filter(p => p.quantity <= p.minStock && p.quantity > 0).length;

    return {
      totalSales,
      totalInvoices,
      totalProducts,
      lowStockProducts
    };
  };

  // Barcode validation
  const isValidScannedCode = (code) => {
    return code && code.trim().length > 0 && code.trim() !== '';
  };

  // Scanner input handlers
  const handleDashboardScannerInput = (e) => {
    const value = e.target.value;
    setDashboardScannerInput(value);

    if (value.trim()) {
      const foundProduct = products.find(p => p.barcode === value.trim());
      if (foundProduct) {
        setDashboardLcdDisplay({
          name: foundProduct.name,
          price: foundProduct.sellPrice || foundProduct.price
        });
      } else {
        setDashboardLcdDisplay(null);
      }
    } else {
      setDashboardLcdDisplay(null);
    }
  };

  const handleDashboardScannerKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      const barcode = dashboardScannerInput.trim();
      
      if (barcode && isValidScannedCode(barcode)) {
        const foundProduct = products.find(p => p.barcode === barcode);
        if (foundProduct) {
          // Open sales modal with the scanned product
          const newInvoice = {
            invoiceNumber: 'INV-' + Date.now(),
            date: new Date().toISOString().split('T')[0],
            customerId: 'GUEST',
            customerName: t('walkInCustomer', 'زبون عابر'),
            paymentMethod: 'نقداً',
            items: [{
              productId: foundProduct.id,
              productName: foundProduct.name,
              quantity: 1,
              price: foundProduct.sellPrice || foundProduct.price,
              total: foundProduct.sellPrice || foundProduct.price
            }],
            total: foundProduct.sellPrice || foundProduct.price,
            discount: 0,
            tax: 0,
            finalTotal: foundProduct.sellPrice || foundProduct.price
          };

          setSalesInvoice(newInvoice);
          setShowSalesModal(true);
          setDashboardScannerInput('');
          setDashboardLcdDisplay(null);
          
          showToast(`✅ ${t('productAddedToInvoice', 'تم إضافة المنتج للفاتورة')}: ${foundProduct.name}`, 'success', 2000);
        } else {
          showToast(`❌ ${t('productNotFound', 'المنتج غير موجود')}: ${barcode}`, 'error', 3000);
        }
      }
    }
  };

  // Bulk operations
  const toggleSelectAll = (type, items) => {
    switch(type) {
      case 'sales':
        if (selectedSalesInvoices.length === items.length) {
          setSelectedSalesInvoices([]);
        } else {
          setSelectedSalesInvoices(items.map(item => item.id));
        }
        break;
      case 'purchases':
        if (selectedPurchases.length === items.length) {
          setSelectedPurchases([]);
        } else {
          setSelectedPurchases(items.map(item => item.id));
        }
        break;
      case 'customers':
        if (selectedCustomers.length === items.length) {
          setSelectedCustomers([]);
        } else {
          setSelectedCustomers(items.map(item => item.id));
        }
        break;
      case 'products':
        if (selectedProducts.length === items.length) {
          setSelectedProducts([]);
        } else {
          setSelectedProducts(items.map(item => item.id));
        }
        break;
      case 'suppliers':
        if (selectedSuppliers.length === items.length) {
          setSelectedSuppliers([]);
        } else {
          setSelectedSuppliers(items.map(item => item.id));
        }
        break;
    }
  };

  const toggleSelectItem = (type, itemId) => {
    switch(type) {
      case 'sales':
        setSelectedSalesInvoices(prev =>
          prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId]
        );
        break;
      case 'purchases':
        setSelectedPurchases(prev =>
          prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId]
        );
        break;
      case 'customers':
        setSelectedCustomers(prev =>
          prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId]
        );
        break;
      case 'products':
        setSelectedProducts(prev =>
          prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId]
        );
        break;
      case 'suppliers':
        setSelectedSuppliers(prev =>
          prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId]
        );
        break;
    }
  };

  const deleteSelectedItems = (type, customIds = null) => {
    const idsToDelete = customIds || (() => {
      switch(type) {
        case 'sales': return selectedSalesInvoices;
        case 'purchases': return selectedPurchases;
        case 'customers': return selectedCustomers;
        case 'products': return selectedProducts;
        case 'suppliers': return selectedSuppliers;
        default: return [];
      }
    })();

    if (idsToDelete.length === 0) {
      showToast(`⚠️ ${t('pleaseSelectItems', 'يرجى تحديد عناصر للحذف')}`, 'warning', 3000);
      return;
    }

    const confirmMessage = t('confirmDelete', 'هل أنت متأكد من حذف العناصر المحددة؟');
    if (window.confirm(confirmMessage)) {
      switch(type) {
        case 'sales':
          const updatedInvoices = savedInvoices.filter(item => !idsToDelete.includes(item.id));
          setSavedInvoices(updatedInvoices);
          saveInvoices(updatedInvoices);
          setSelectedSalesInvoices([]);
          break;
        case 'purchases':
          const updatedPurchases = savedPurchases.filter(item => !idsToDelete.includes(item.id));
          setSavedPurchases(updatedPurchases);
          savePurchases(updatedPurchases);
          setSelectedPurchases([]);
          break;
        case 'customers':
          const updatedCustomers = customers.filter(item => !idsToDelete.includes(item.id));
          setCustomers(updatedCustomers);
          saveCustomers(updatedCustomers);
          setSelectedCustomers([]);
          break;
        case 'products':
          const updatedProducts = products.filter(item => !idsToDelete.includes(item.id));
          setProducts(updatedProducts);
          saveProducts(updatedProducts);
          setSelectedProducts([]);
          break;
        case 'suppliers':
          const updatedSuppliers = suppliers.filter(item => !idsToDelete.includes(item.id));
          setSuppliers(updatedSuppliers);
          saveSuppliers(updatedSuppliers);
          setSelectedSuppliers([]);
          break;
      }
      
      showToast(`✅ ${t('itemsDeleted', 'تم حذف العناصر المحددة')}`, 'success', 2000);
    }
  };

  // Print functions (simplified versions)
  const printInvoice = (invoice) => {
    // Implementation for regular printing
    showToast(`🖨️ ${t('printingInvoice', 'جاري طباعة الفاتورة')}...`, 'info', 2000);
  };

  const thermalPrintInvoice = (invoice) => {
    // Implementation for thermal printing
    showToast(`🎫 ${t('thermalPrinting', 'جاري الطباعة الحرارية')}...`, 'info', 2000);
  };

  // Render current page
  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <Dashboard
            dashboardScannerInput={dashboardScannerInput}
            setDashboardScannerInput={setDashboardScannerInput}
            dashboardScannerRef={dashboardScannerRef}
            dashboardLcdDisplay={dashboardLcdDisplay}
            setDashboardLcdDisplay={setDashboardLcdDisplay}
            dashboardTotalDisplay={dashboardTotalDisplay}
            setDashboardTotalDisplay={setDashboardTotalDisplay}
            products={products}
            handleDashboardScannerInput={handleDashboardScannerInput}
            handleDashboardScannerKeyPress={handleDashboardScannerKeyPress}
            isValidScannedCode={isValidScannedCode}
            setSalesInvoice={setSalesInvoice}
            setShowSalesModal={setShowSalesModal}
            setSalesScannerInput={setSalesScannerInput}
            navigateToPage={navigateToPage}
            showToast={showToast}
            t={t}
            currentLanguage={currentLanguage}
            savedInvoices={savedInvoices}
            formatPrice={formatPrice}
            calculateDashboardStats={calculateDashboardStats}
          />
        );

      case 'sales':
        return (
          <SalesManagement
            salesInvoice={salesInvoice}
            setSalesInvoice={setSalesInvoice}
            showSalesModal={showSalesModal}
            setShowSalesModal={setShowSalesModal}
            products={products}
            customers={customers}
            savedInvoices={savedInvoices}
            setSavedInvoices={setSavedInvoices}
            salesScannerInput={salesScannerInput}
            setSalesScannerInput={setSalesScannerInput}
            salesScannerRef={salesScannerRef}
            salesLcdDisplay={salesLcdDisplay}
            setSalesLcdDisplay={setSalesLcdDisplay}
            salesTotalDisplay={salesTotalDisplay}
            setSalesTotalDisplay={setSalesTotalDisplay}
            selectedSalesInvoices={selectedSalesInvoices}
            setSelectedSalesInvoices={setSelectedSalesInvoices}
            salesInvoiceNumberFilter={salesInvoiceNumberFilter}
            setSalesInvoiceNumberFilter={setSalesInvoiceNumberFilter}
            salesCustomerFilter={salesCustomerFilter}
            setSalesCustomerFilter={setSalesCustomerFilter}
            salesDateFilter={salesDateFilter}
            setSalesDateFilter={setSalesDateFilter}
            salesPaymentMethodFilter={salesPaymentMethodFilter}
            setSalesPaymentMethodFilter={setSalesPaymentMethodFilter}
            salesStatusFilter={salesStatusFilter}
            setSalesStatusFilter={setSalesStatusFilter}
            showToast={showToast}
            formatPrice={formatPrice}
            t={t}
            currentLanguage={currentLanguage}
            currentUser={currentUser}
            isValidScannedCode={isValidScannedCode}
            toggleSelectAll={toggleSelectAll}
            toggleSelectItem={toggleSelectItem}
            deleteSelectedItems={deleteSelectedItems}
            printInvoice={printInvoice}
            thermalPrintInvoice={thermalPrintInvoice}
          />
        );

      case 'inventory':
        return (
          <InventoryManagement
            products={products}
            setProducts={setProducts}
            suppliers={suppliers}
            categories={categories}
            setCategories={setCategories}
            showProductModal={showProductModal}
            setShowProductModal={setShowProductModal}
            showCategoryModal={showCategoryModal}
            setShowCategoryModal={setShowCategoryModal}
            newProduct={newProduct}
            setNewProduct={setNewProduct}
            selectedProducts={selectedProducts}
            setSelectedProducts={setSelectedProducts}
            productSearch={productSearch}
            setProductSearch={setProductSearch}
            selectedCategory={selectedCategory}
            setSelectedCategory={setSelectedCategory}
            selectedStatus={selectedStatus}
            setSelectedStatus={setSelectedStatus}
            showToast={showToast}
            formatPrice={formatPrice}
            t={t}
            currentLanguage={currentLanguage}
            currentUser={currentUser}
            toggleSelectAll={toggleSelectAll}
            toggleSelectItem={toggleSelectItem}
            deleteSelectedItems={deleteSelectedItems}
            saveProducts={saveProducts}
            saveCategories={saveCategories}
          />
        );

      case 'customers':
        return (
          <CustomerPage
            // Pass all necessary props for customer management
            customers={customers}
            setCustomers={setCustomers}
            showToast={showToast}
            formatPrice={formatPrice}
            t={t}
            currentLanguage={currentLanguage}
            currentUser={currentUser}
          />
        );

      case 'repair':
        return (
          <RepairPage
            // Pass all necessary props for repair management
            savedRepairs={savedRepairs}
            setSavedRepairs={setSavedRepairs}
            showToast={showToast}
            formatPrice={formatPrice}
            t={t}
            currentLanguage={currentLanguage}
            currentUser={currentUser}
          />
        );

      default:
        return (
          <div className="page-not-found">
            <h2>{t('pageNotFound', 'الصفحة غير موجودة')}</h2>
            <button onClick={() => navigateToPage('dashboard')}>
              {t('backToDashboard', 'العودة للوحة التحكم')}
            </button>
          </div>
        );
    }
  };

  return (
    <div className={`app lang-${currentLanguage}`}>
      {/* Sidebar Navigation */}
      <aside className="sidebar">
        <div className="sidebar-header">
          <div className="logo">
            <h2>iCalDZ</h2>
            <span>{t('accountingSystem', 'نظام المحاسبة')}</span>
          </div>
        </div>

        <nav className="sidebar-nav">
          <button
            className={`nav-item ${currentPage === 'dashboard' ? 'active' : ''}`}
            onClick={() => navigateToPage('dashboard')}
          >
            <span className="nav-icon">🏠</span>
            <span className="nav-text">{t('dashboard', 'لوحة التحكم')}</span>
          </button>
          <button
            className={`nav-item ${currentPage === 'sales' ? 'active' : ''}`}
            onClick={() => navigateToPage('sales')}
          >
            <span className="nav-icon">🛒</span>
            <span className="nav-text">{t('sales', 'المبيعات')}</span>
          </button>
          <button
            className={`nav-item ${currentPage === 'repair' ? 'active' : ''}`}
            onClick={() => navigateToPage('repair')}
          >
            <span className="nav-icon">🔧</span>
            <span className="nav-text">{t('repair', 'الإصلاح')}</span>
          </button>
          <button
            className={`nav-item ${currentPage === 'purchases' ? 'active' : ''}`}
            onClick={() => navigateToPage('purchases')}
          >
            <span className="nav-icon">📦</span>
            <span className="nav-text">{t('purchases', 'المشتريات')}</span>
          </button>
          <button
            className={`nav-item ${currentPage === 'customers' ? 'active' : ''}`}
            onClick={() => navigateToPage('customers')}
          >
            <span className="nav-icon">👥</span>
            <span className="nav-text">{t('customers', 'العملاء')}</span>
          </button>
          <button
            className={`nav-item ${currentPage === 'inventory' ? 'active' : ''}`}
            onClick={() => navigateToPage('inventory')}
          >
            <span className="nav-icon">📋</span>
            <span className="nav-text">{t('inventory', 'المخزون')}</span>
          </button>
          <button
            className={`nav-item ${currentPage === 'reports' ? 'active' : ''}`}
            onClick={() => navigateToPage('reports')}
          >
            <span className="nav-icon">📊</span>
            <span className="nav-text">{t('reports', 'التقارير')}</span>
          </button>
          <button
            className={`nav-item ${currentPage === 'settings' ? 'active' : ''}`}
            onClick={() => navigateToPage('settings')}
          >
            <span className="nav-icon">⚙️</span>
            <span className="nav-text">{t('settings', 'الإعدادات')}</span>
          </button>
        </nav>
      </aside>

      {/* Main Content */}
      <main className="main-content">
        {renderCurrentPage()}
      </main>

      {/* Toast Notifications */}
      <div className="toast-container">
        {toasts.map(toast => (
          <div key={toast.id} className={`toast toast-${toast.type}`}>
            {toast.message}
          </div>
        ))}
      </div>

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <button
          className="scroll-to-top"
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        >
          ↑
        </button>
      )}
    </div>
  );
};

export default AppRouter;
