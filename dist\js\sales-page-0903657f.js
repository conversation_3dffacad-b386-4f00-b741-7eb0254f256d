import{j as e}from"./react-vendor-03c8a839.js";import"./i18n-121a6c54.js";const s=({salesInvoice:s,setSalesInvoice:t,showSalesModal:a,setShowSalesModal:l,products:n,customers:i,savedInvoices:c,setSavedInvoices:r,salesScannerInput:d,setSalesScannerInput:o,salesScannerRef:h,salesLcdDisplay:m,setSalesLcdDisplay:u,salesTotalDisplay:x,setSalesTotalDisplay:j,selectedSalesInvoices:v,setSelectedSalesInvoices:p,salesInvoiceNumberFilter:N,setSalesInvoiceNumberFilter:b,salesCustomerFilter:g,setSalesCustomerFilter:S,salesDateFilter:f,setSalesDateFilter:y,salesPaymentMethodFilter:I,setSalesPaymentMethodFilter:C,salesStatusFilter:k,setSalesStatusFilter:w,showToast:D,formatPrice:F,t:M,currentLanguage:T,currentUser:L,handleSalesScannerInput:P,handleSalesScannerKeyPress:A,isValidScannedCode:B,toggleSelectAll:U,toggleSelectItem:V,deleteSelectedItems:E,printInvoice:G,thermalPrintInvoice:K})=>{const O=c.filter((e=>{const s=!N||e.invoiceNumber.toLowerCase().includes(N.toLowerCase()),t=!g||e.customerName.toLowerCase().includes(g.toLowerCase()),a=!f||e.date===f,l=!I||e.paymentMethod===I,n=!k||e.status===k;return s&&t&&a&&l&&n}));return e.jsxs("div",{className:"sales-page",children:[e.jsxs("div",{className:"page-header "+("ar"!==T?"page-header-ltr-split":""),children:[e.jsx("div",{className:"page-title-section",children:e.jsxs("h1",{children:["🛒 ",M("salesInvoices","فواتير المبيعات")]})}),e.jsx("div",{className:"page-actions",children:e.jsxs("button",{className:"btn btn-primary",onClick:()=>{const e={invoiceNumber:"INV-"+Date.now(),date:(new Date).toISOString().split("T")[0],customerId:"GUEST",customerName:M("walkInCustomer","زبون عابر"),paymentMethod:"نقداً",items:[],total:0,discount:0,tax:0,finalTotal:0};t(e),o(""),u(null),j(null),l(!0)},children:[e.jsx("span",{className:"btn-icon",children:"➕"}),M("newSalesInvoice","فاتورة مبيعات جديدة")]})})]}),e.jsxs("div",{className:"filters-section",children:[e.jsxs("div",{className:"filters-grid",children:[e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:M("invoiceNumber","رقم الفاتورة")}),e.jsx("input",{type:"text",placeholder:M("searchByInvoiceNumber","البحث برقم الفاتورة"),value:N,onChange:e=>b(e.target.value),className:"filter-input"})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:M("customer","العميل")}),e.jsx("input",{type:"text",placeholder:M("searchByCustomer","البحث بالعميل"),value:g,onChange:e=>S(e.target.value),className:"filter-input"})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:M("date","التاريخ")}),e.jsx("input",{type:"date",value:f,onChange:e=>y(e.target.value),className:"filter-input"})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:M("paymentMethod","طريقة الدفع")}),e.jsxs("select",{value:I,onChange:e=>C(e.target.value),className:"filter-select",children:[e.jsx("option",{value:"",children:M("all","الكل")}),e.jsx("option",{value:"نقداً",children:M("cash","نقداً")}),e.jsx("option",{value:"آجل",children:M("credit","آجل")}),e.jsx("option",{value:"بطاقة",children:M("card","بطاقة")})]})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:M("status","الحالة")}),e.jsxs("select",{value:k,onChange:e=>w(e.target.value),className:"filter-select",children:[e.jsx("option",{value:"",children:M("all","الكل")}),e.jsx("option",{value:"مدفوعة",children:M("paid","مدفوعة")}),e.jsx("option",{value:"غير مدفوعة",children:M("unpaid","غير مدفوعة")}),e.jsx("option",{value:"مرتجعة",children:M("returned","مرتجعة")})]})]})]}),e.jsx("div",{className:"filters-actions",children:e.jsx("button",{className:"btn btn-secondary",onClick:()=>{b(""),S(""),y(""),C(""),w("")},children:M("clearFilters","مسح المرشحات")})})]}),v.length>0&&e.jsxs("div",{className:"bulk-actions",children:[e.jsxs("span",{className:"bulk-count",children:[v.length," ",M("itemsSelected","عنصر محدد")]}),e.jsx("button",{className:"btn btn-danger",onClick:()=>E("sales"),children:M("deleteSelected","حذف المحدد")})]}),e.jsx("div",{className:"table-container",children:e.jsxs("table",{className:"data-table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:e.jsx("input",{type:"checkbox",checked:v.length===O.length&&O.length>0,onChange:()=>U("sales",O)})}),e.jsx("th",{children:M("invoiceNumber","رقم الفاتورة")}),e.jsx("th",{children:M("customer","العميل")}),e.jsx("th",{children:M("date","التاريخ")}),e.jsx("th",{children:M("total","الإجمالي")}),e.jsx("th",{children:M("paymentMethod","طريقة الدفع")}),e.jsx("th",{children:M("status","الحالة")}),e.jsx("th",{children:M("actions","الإجراءات")})]})}),e.jsx("tbody",{children:O.map((s=>e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("input",{type:"checkbox",checked:v.includes(s.id),onChange:()=>V("sales",s.id)})}),e.jsx("td",{children:s.invoiceNumber}),e.jsx("td",{children:s.customerName}),e.jsx("td",{children:new Date(s.date).toLocaleDateString("ar-DZ")}),e.jsx("td",{children:F(s.finalTotal)}),e.jsx("td",{children:s.paymentMethod}),e.jsx("td",{children:e.jsx("span",{className:"status-badge status-"+("مدفوعة"===s.status?"paid":"unpaid"),children:s.status||"مدفوعة"})}),e.jsx("td",{children:e.jsxs("div",{className:"action-buttons",children:[e.jsx("button",{className:"btn btn-sm btn-info",onClick:()=>G(s),title:M("print","طباعة"),children:"🖨️"}),e.jsx("button",{className:"btn btn-sm btn-success",onClick:()=>K(s),title:M("thermalPrint","طباعة حرارية"),children:"🎫"}),("مدير"===L.role||"admin"===L.role)&&e.jsx("button",{className:"btn btn-sm btn-warning",onClick:()=>{t(s),l(!0)},title:M("edit","تعديل"),children:"✏️"})]})})]},s.id)))})]})}),e.jsx("div",{className:"sales-stats",children:e.jsxs("div",{className:"stats-grid",children:[e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-icon",children:"📊"}),e.jsxs("div",{className:"stat-content",children:[e.jsx("h3",{children:M("totalSalesInvoices","إجمالي فواتير المبيعات")}),e.jsx("div",{className:"stat-value",children:O.length})]})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-icon",children:"💰"}),e.jsxs("div",{className:"stat-content",children:[e.jsx("h3",{children:M("totalSalesAmount","إجمالي مبلغ المبيعات")}),e.jsx("div",{className:"stat-value",children:F(O.reduce(((e,s)=>e+s.finalTotal),0))})]})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-icon",children:"💳"}),e.jsxs("div",{className:"stat-content",children:[e.jsx("h3",{children:M("cashSales","المبيعات النقدية")}),e.jsx("div",{className:"stat-value",children:F(O.filter((e=>"نقداً"===e.paymentMethod)).reduce(((e,s)=>e+s.finalTotal),0))})]})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-icon",children:"📅"}),e.jsxs("div",{className:"stat-content",children:[e.jsx("h3",{children:M("creditSales","المبيعات الآجلة")}),e.jsx("div",{className:"stat-value",children:F(O.filter((e=>"آجل"===e.paymentMethod)).reduce(((e,s)=>e+s.finalTotal),0))})]})]})]})})]})};export{s as S};
