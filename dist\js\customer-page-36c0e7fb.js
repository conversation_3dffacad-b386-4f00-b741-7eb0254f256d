import{j as e}from"./react-vendor-03c8a839.js";const s=({customerManagement:s,currentLanguage:a,t:l,formatPrice:r})=>{const{showCustomerModal:t,setShowCustomerModal:n,showEditCustomerModal:d,setShowEditCustomerModal:c,showDeleteCustomerModal:o,setShowDeleteCustomerModal:i,editingCustomer:m,setEditingCustomer:h,customerToDelete:x,setCustomerToDelete:u,newCustomer:j,setNewCustomer:N,customerNameFilter:v,setCustomerNameFilter:p,customerPhoneFilter:C,setCustomerPhoneFilter:b,filteredCustomers:g,handleCreateCustomer:y,handleEditCustomer:f,handleDeleteCustomer:k,resetCustomerForm:w}=s;return e.jsxs("div",{className:"customers-page",children:[e.jsxs("div",{className:"page-header "+("ar"!==a?"page-header-ltr-split":""),children:[e.jsx("div",{className:"page-title-section",children:e.jsxs("h1",{children:["👥 ",l("customersManagement","إدارة الزبائن")]})}),e.jsx("div",{className:"page-description-section",children:e.jsxs("button",{className:"btn btn-primary",onClick:()=>n(!0),children:["+ ",l("addNewCustomer","إضافة زبون جديد")]})})]}),e.jsx("div",{className:"filters-section",children:e.jsxs("div",{className:"filters-row",children:[e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:l("filterByName","تصفية حسب الاسم")}),e.jsx("input",{type:"text",value:v,onChange:e=>p(e.target.value),placeholder:l("customerName","اسم العميل")})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:l("filterByPhone","تصفية حسب الهاتف")}),e.jsx("input",{type:"text",value:C,onChange:e=>b(e.target.value),placeholder:l("phoneNumber","رقم الهاتف")})]}),e.jsx("div",{className:"filter-actions",children:e.jsxs("button",{className:"btn btn-secondary",onClick:()=>{p(""),b("")},children:["🗑️ ",l("clearFilters","مسح المرشحات")]})})]})}),e.jsx("div",{className:"summary-cards",children:e.jsxs("div",{className:"summary-card",children:[e.jsx("div",{className:"summary-icon",children:"👥"}),e.jsxs("div",{className:"summary-content",children:[e.jsx("div",{className:"summary-number",children:g.length}),e.jsx("div",{className:"summary-label",children:l("totalCustomers","إجمالي العملاء")})]})]})}),e.jsx("div",{className:"table-section",children:e.jsx("div",{className:"table-container",children:e.jsxs("table",{className:"data-table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:l("customerName","اسم العميل")}),e.jsx("th",{children:l("phoneNumber","رقم الهاتف")}),e.jsx("th",{children:l("email","البريد الإلكتروني")}),e.jsx("th",{children:l("address","العنوان")}),e.jsx("th",{children:l("notes","ملاحظات")}),e.jsx("th",{children:l("createdAt","تاريخ الإنشاء")}),e.jsx("th",{children:l("actions","الإجراءات")})]})}),e.jsxs("tbody",{children:[g.map((s=>e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("div",{className:"customer-name",children:e.jsx("strong",{children:s.name})})}),e.jsx("td",{children:e.jsx("div",{className:"customer-phone",children:s.phone||l("noPhone","لا يوجد هاتف")})}),e.jsx("td",{children:e.jsx("div",{className:"customer-email",children:s.email||l("noEmail","لا يوجد بريد إلكتروني")})}),e.jsx("td",{children:e.jsx("div",{className:"customer-address",children:s.address||l("noAddress","لا يوجد عنوان")})}),e.jsx("td",{children:e.jsx("div",{className:"customer-notes",children:s.notes||l("noNotes","لا توجد ملاحظات")})}),e.jsx("td",{children:e.jsx("div",{className:"customer-date",children:new Date(s.createdAt).toLocaleDateString("ar"===a?"ar-DZ":"fr"===a?"fr-FR":"en-US")})}),e.jsx("td",{children:e.jsxs("div",{className:"customer-actions",children:[e.jsx("button",{className:"btn btn-sm btn-info",onClick:()=>{h(s),c(!0)},title:l("editCustomer","تعديل العميل"),children:"✏️"}),e.jsx("button",{className:"btn btn-sm btn-danger",onClick:()=>{u(s),i(!0)},title:l("deleteCustomer","حذف العميل"),children:"🗑️"})]})})]},s.id))),0===g.length&&e.jsx("tr",{children:e.jsx("td",{colSpan:"7",style:{textAlign:"center",padding:"40px"},children:e.jsxs("div",{className:"no-data-message",children:[e.jsx("div",{className:"no-data-icon",children:"👥"}),e.jsx("h3",{children:l("noCustomersFound","لا توجد عملاء")}),e.jsx("p",{children:l("createFirstCustomer",'قم بإنشاء أول عميل باستخدام زر "إضافة زبون جديد"')})]})})})]})]})})}),t&&e.jsx("div",{className:"modal-overlay",onClick:()=>n(!1),children:e.jsxs("div",{className:"modal-content",onClick:e=>e.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsx("h3",{children:l("addNewCustomer","إضافة زبون جديد")}),e.jsx("button",{className:"modal-close",onClick:()=>n(!1),children:"×"})]}),e.jsxs("div",{className:"modal-body",children:[e.jsxs("div",{className:"form-group",children:[e.jsxs("label",{children:[l("customerName","اسم العميل")," *"]}),e.jsx("input",{type:"text",value:j.name,onChange:e=>N({...j,name:e.target.value}),placeholder:l("enterCustomerName","أدخل اسم العميل"),required:!0})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:l("phoneNumber","رقم الهاتف")}),e.jsx("input",{type:"tel",value:j.phone,onChange:e=>N({...j,phone:e.target.value}),placeholder:l("enterPhoneNumber","أدخل رقم الهاتف")})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:l("email","البريد الإلكتروني")}),e.jsx("input",{type:"email",value:j.email,onChange:e=>N({...j,email:e.target.value}),placeholder:l("enterEmail","أدخل البريد الإلكتروني")})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:l("address","العنوان")}),e.jsx("textarea",{value:j.address,onChange:e=>N({...j,address:e.target.value}),placeholder:l("enterAddress","أدخل العنوان"),rows:"3"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:l("notes","ملاحظات")}),e.jsx("textarea",{value:j.notes,onChange:e=>N({...j,notes:e.target.value}),placeholder:l("enterNotes","أدخل ملاحظات"),rows:"3"})]})]}),e.jsxs("div",{className:"modal-footer",children:[e.jsx("button",{className:"btn btn-secondary",onClick:()=>n(!1),children:l("cancel","إلغاء")}),e.jsx("button",{className:"btn btn-primary",onClick:y,children:l("addCustomer","إضافة العميل")})]})]})}),d&&m&&e.jsx("div",{className:"modal-overlay",onClick:()=>c(!1),children:e.jsxs("div",{className:"modal-content",onClick:e=>e.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsx("h3",{children:l("editCustomer","تعديل العميل")}),e.jsx("button",{className:"modal-close",onClick:()=>c(!1),children:"×"})]}),e.jsxs("div",{className:"modal-body",children:[e.jsxs("div",{className:"form-group",children:[e.jsxs("label",{children:[l("customerName","اسم العميل")," *"]}),e.jsx("input",{type:"text",value:m.name,onChange:e=>h({...m,name:e.target.value}),placeholder:l("enterCustomerName","أدخل اسم العميل"),required:!0})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:l("phoneNumber","رقم الهاتف")}),e.jsx("input",{type:"tel",value:m.phone,onChange:e=>h({...m,phone:e.target.value}),placeholder:l("enterPhoneNumber","أدخل رقم الهاتف")})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:l("email","البريد الإلكتروني")}),e.jsx("input",{type:"email",value:m.email,onChange:e=>h({...m,email:e.target.value}),placeholder:l("enterEmail","أدخل البريد الإلكتروني")})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:l("address","العنوان")}),e.jsx("textarea",{value:m.address,onChange:e=>h({...m,address:e.target.value}),placeholder:l("enterAddress","أدخل العنوان"),rows:"3"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:l("notes","ملاحظات")}),e.jsx("textarea",{value:m.notes,onChange:e=>h({...m,notes:e.target.value}),placeholder:l("enterNotes","أدخل ملاحظات"),rows:"3"})]})]}),e.jsxs("div",{className:"modal-footer",children:[e.jsx("button",{className:"btn btn-secondary",onClick:()=>c(!1),children:l("cancel","إلغاء")}),e.jsx("button",{className:"btn btn-primary",onClick:f,children:l("updateCustomer","تحديث العميل")})]})]})}),o&&x&&e.jsx("div",{className:"modal-overlay",onClick:()=>i(!1),children:e.jsxs("div",{className:"modal-content",onClick:e=>e.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsx("h3",{children:l("deleteCustomer","حذف العميل")}),e.jsx("button",{className:"modal-close",onClick:()=>i(!1),children:"×"})]}),e.jsxs("div",{className:"modal-body",children:[e.jsx("p",{children:l("confirmDeleteCustomer","هل أنت متأكد من حذف هذا العميل؟")}),e.jsx("p",{children:e.jsx("strong",{children:x.name})})]}),e.jsxs("div",{className:"modal-footer",children:[e.jsx("button",{className:"btn btn-secondary",onClick:()=>i(!1),children:l("cancel","إلغاء")}),e.jsx("button",{className:"btn btn-danger",onClick:k,children:l("delete","حذف")})]})]})})]})};export{s as C};
