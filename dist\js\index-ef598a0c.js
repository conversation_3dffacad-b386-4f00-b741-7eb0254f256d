import{r as e,j as t,b as n,R as s}from"./react-vendor-03c8a839.js";import{u as a,L as r,a as i}from"./i18n-121a6c54.js";import{u as o,A as c}from"./app-state-ae10b14c.js";import{D as l}from"./dashboard-page-f97c59d7.js";import{S as d}from"./sales-page-0903657f.js";import{I as u}from"./inventory-page-20e3a0b9.js";import{C as p}from"./customer-page-36c0e7fb.js";import{R as m}from"./repair-page-c81fcae5.js";import{S as h}from"./media-82034546.js";import"./thermal-6474eea0.js";import{m as g}from"./memory-7bd07dd4.js";import{a as v}from"./data-6984d4ae.js";import"./vendor-38106ca9.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const x=()=>{const{t:n,currentLanguage:s}=a(),{currentPage:r,setCurrentPage:i,isLoggedIn:c,setIsLoggedIn:h,products:g,setProducts:v,customers:x,setCustomers:f,suppliers:b,setSuppliers:S,savedInvoices:j,setSavedInvoices:w,savedPurchases:y,setSavedPurchases:N,savedRepairs:I,setSavedRepairs:C,categories:P,setCategories:D,storeSettings:k,setStoreSettings:F,currentUser:L,setCurrentUser:M,soundEnabled:T,setSoundEnabled:E,shortcutsEnabled:A,setShortcutsEnabled:z,printerEnabled:R,setPrinterEnabled:$,notificationsEnabled:U,setNotificationsEnabled:Z,showScrollTop:q,setShowScrollTop:O,toasts:Y,setToasts:B,dashboardScannerInput:K,setDashboardScannerInput:V,salesScannerInput:J,setSalesScannerInput:G,editScannerInput:H,setEditScannerInput:Q,dashboardLcdDisplay:W,setDashboardLcdDisplay:X,salesLcdDisplay:_,setSalesLcdDisplay:ee,editLcdDisplay:te,setEditLcdDisplay:ne,dashboardTotalDisplay:se,setDashboardTotalDisplay:ae,salesTotalDisplay:re,setSalesTotalDisplay:ie,editTotalDisplay:oe,setEditTotalDisplay:ce,showSalesModal:le,setShowSalesModal:de,showPurchaseModal:ue,setShowPurchaseModal:pe,showProductModal:me,setShowProductModal:he,showCustomerModal:ge,setShowCustomerModal:ve,showSupplierModal:xe,setShowSupplierModal:fe,showCategoryModal:be,setShowCategoryModal:Se,showSettingsModal:je,setShowSettingsModal:we,salesInvoice:ye,setSalesInvoice:Ne,purchaseInvoice:Ie,setPurchaseInvoice:Ce,newProduct:Pe,setNewProduct:De,newCustomer:ke,setNewCustomer:Fe,newSupplier:Le,setNewSupplier:Me,selectedSalesInvoices:Te,setSelectedSalesInvoices:Ee,selectedPurchases:Ae,setSelectedPurchases:ze,selectedCustomers:Re,setSelectedCustomers:$e,selectedProducts:Ue,setSelectedProducts:Ze,selectedSuppliers:qe,setSelectedSuppliers:Oe,productSearch:Ye,setProductSearch:Be,selectedCategory:Ke,setSelectedCategory:Ve,selectedStatus:Je,setSelectedStatus:Ge,customerFilter:He,setCustomerFilter:Qe,salesInvoiceFilter:We,setSalesInvoiceFilter:Xe,purchaseInvoiceFilter:_e,setPurchaseInvoiceFilter:et,salesInvoiceNumberFilter:tt,setSalesInvoiceNumberFilter:nt,salesCustomerFilter:st,setSalesCustomerFilter:at,salesDateFilter:rt,setSalesDateFilter:it,salesPaymentMethodFilter:ot,setSalesPaymentMethodFilter:ct,salesStatusFilter:lt,setSalesStatusFilter:dt,purchaseInvoiceNumberFilter:ut,setPurchaseInvoiceNumberFilter:pt,purchaseSupplierFilter:mt,setPurchaseSupplierFilter:ht,purchaseDateFilter:gt,setPurchaseDateFilter:vt,purchasePaymentMethodFilter:xt,setPurchasePaymentMethodFilter:ft,purchaseStatusFilter:bt,setPurchaseStatusFilter:St,saveProducts:jt,saveCustomers:wt,saveSuppliers:yt,saveInvoices:Nt,savePurchases:It,saveRepairs:Ct,saveCategories:Pt,saveStoreSettings:Dt}=o(),kt=e.useRef(null),Ft=e.useRef(null);e.useRef(null);const Lt=(e,t="info",n=3e3)=>{const s=Date.now(),a={id:s,message:e,type:t,duration:n};B((e=>[...e,a])),setTimeout((()=>{B((e=>e.filter((e=>e.id!==s))))}),n)},Mt=e=>{const t=parseFloat(e)||0;return new Intl.NumberFormat("ar-DZ",{style:"currency",currency:k.currency||"DZD",minimumFractionDigits:2}).format(t)},Tt=e=>{i(e),localStorage.setItem("icaldz-current-page",e),V(""),G(""),Q(""),X(null),ee(null),ne(null),ae(null),ie(null),ce(null),setTimeout((()=>{"dashboard"===e&&kt.current?kt.current.focus():"sales"===e&&Ft.current&&Ft.current.focus()}),100)},Et=()=>({totalSales:j.reduce(((e,t)=>e+t.finalTotal),0),totalInvoices:j.length,totalProducts:g.length,lowStockProducts:g.filter((e=>e.quantity<=e.minStock&&e.quantity>0)).length}),At=e=>e&&e.trim().length>0&&""!==e.trim(),zt=e=>{const t=e.target.value;if(V(t),t.trim()){const e=g.find((e=>e.barcode===t.trim()));X(e?{name:e.name,price:e.sellPrice||e.price}:null)}else X(null)},Rt=e=>{if("Enter"===e.key){e.preventDefault();const t=K.trim();if(t&&At(t)){const e=g.find((e=>e.barcode===t));if(e){const t={invoiceNumber:"INV-"+Date.now(),date:(new Date).toISOString().split("T")[0],customerId:"GUEST",customerName:n("walkInCustomer","زبون عابر"),paymentMethod:"نقداً",items:[{productId:e.id,productName:e.name,quantity:1,price:e.sellPrice||e.price,total:e.sellPrice||e.price}],total:e.sellPrice||e.price,discount:0,tax:0,finalTotal:e.sellPrice||e.price};Ne(t),de(!0),V(""),X(null),Lt(`✅ ${n("productAddedToInvoice","تم إضافة المنتج للفاتورة")}: ${e.name}`,"success",2e3)}else Lt(`❌ ${n("productNotFound","المنتج غير موجود")}: ${t}`,"error",3e3)}}},$t=(e,t)=>{switch(e){case"sales":Te.length===t.length?Ee([]):Ee(t.map((e=>e.id)));break;case"purchases":Ae.length===t.length?ze([]):ze(t.map((e=>e.id)));break;case"customers":Re.length===t.length?$e([]):$e(t.map((e=>e.id)));break;case"products":Ue.length===t.length?Ze([]):Ze(t.map((e=>e.id)));break;case"suppliers":qe.length===t.length?Oe([]):Oe(t.map((e=>e.id)))}},Ut=(e,t)=>{switch(e){case"sales":Ee((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]));break;case"purchases":ze((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]));break;case"customers":$e((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]));break;case"products":Ze((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]));break;case"suppliers":Oe((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]))}},Zt=(e,t=null)=>{const s=t||(()=>{switch(e){case"sales":return Te;case"purchases":return Ae;case"customers":return Re;case"products":return Ue;case"suppliers":return qe;default:return[]}})();if(0===s.length)return void Lt(`⚠️ ${n("pleaseSelectItems","يرجى تحديد عناصر للحذف")}`,"warning",3e3);const a=n("confirmDelete","هل أنت متأكد من حذف العناصر المحددة؟");if(window.confirm(a)){switch(e){case"sales":const e=j.filter((e=>!s.includes(e.id)));w(e),Nt(e),Ee([]);break;case"purchases":const t=y.filter((e=>!s.includes(e.id)));N(t),It(t),ze([]);break;case"customers":const n=x.filter((e=>!s.includes(e.id)));f(n),wt(n),$e([]);break;case"products":const a=g.filter((e=>!s.includes(e.id)));v(a),jt(a),Ze([]);break;case"suppliers":const r=b.filter((e=>!s.includes(e.id)));S(r),yt(r),Oe([])}Lt(`✅ ${n("itemsDeleted","تم حذف العناصر المحددة")}`,"success",2e3)}},qt=e=>{Lt(`🖨️ ${n("printingInvoice","جاري طباعة الفاتورة")}...`,"info",2e3)},Ot=e=>{Lt(`🎫 ${n("thermalPrinting","جاري الطباعة الحرارية")}...`,"info",2e3)};return t.jsxs("div",{className:`app lang-${s}`,children:[t.jsxs("aside",{className:"sidebar",children:[t.jsx("div",{className:"sidebar-header",children:t.jsxs("div",{className:"logo",children:[t.jsx("h2",{children:"iCalDZ"}),t.jsx("span",{children:n("accountingSystem","نظام المحاسبة")})]})}),t.jsxs("nav",{className:"sidebar-nav",children:[t.jsxs("button",{className:"nav-item "+("dashboard"===r?"active":""),onClick:()=>Tt("dashboard"),children:[t.jsx("span",{className:"nav-icon",children:"🏠"}),t.jsx("span",{className:"nav-text",children:n("dashboard","لوحة التحكم")})]}),t.jsxs("button",{className:"nav-item "+("sales"===r?"active":""),onClick:()=>Tt("sales"),children:[t.jsx("span",{className:"nav-icon",children:"🛒"}),t.jsx("span",{className:"nav-text",children:n("sales","المبيعات")})]}),t.jsxs("button",{className:"nav-item "+("repair"===r?"active":""),onClick:()=>Tt("repair"),children:[t.jsx("span",{className:"nav-icon",children:"🔧"}),t.jsx("span",{className:"nav-text",children:n("repair","الإصلاح")})]}),t.jsxs("button",{className:"nav-item "+("purchases"===r?"active":""),onClick:()=>Tt("purchases"),children:[t.jsx("span",{className:"nav-icon",children:"📦"}),t.jsx("span",{className:"nav-text",children:n("purchases","المشتريات")})]}),t.jsxs("button",{className:"nav-item "+("customers"===r?"active":""),onClick:()=>Tt("customers"),children:[t.jsx("span",{className:"nav-icon",children:"👥"}),t.jsx("span",{className:"nav-text",children:n("customers","العملاء")})]}),t.jsxs("button",{className:"nav-item "+("inventory"===r?"active":""),onClick:()=>Tt("inventory"),children:[t.jsx("span",{className:"nav-icon",children:"📋"}),t.jsx("span",{className:"nav-text",children:n("inventory","المخزون")})]}),t.jsxs("button",{className:"nav-item "+("reports"===r?"active":""),onClick:()=>Tt("reports"),children:[t.jsx("span",{className:"nav-icon",children:"📊"}),t.jsx("span",{className:"nav-text",children:n("reports","التقارير")})]}),t.jsxs("button",{className:"nav-item "+("settings"===r?"active":""),onClick:()=>Tt("settings"),children:[t.jsx("span",{className:"nav-icon",children:"⚙️"}),t.jsx("span",{className:"nav-text",children:n("settings","الإعدادات")})]})]})]}),t.jsx("main",{className:"main-content",children:(()=>{switch(r){case"dashboard":return t.jsx(l,{dashboardScannerInput:K,setDashboardScannerInput:V,dashboardScannerRef:kt,dashboardLcdDisplay:W,setDashboardLcdDisplay:X,dashboardTotalDisplay:se,setDashboardTotalDisplay:ae,products:g,handleDashboardScannerInput:zt,handleDashboardScannerKeyPress:Rt,isValidScannedCode:At,setSalesInvoice:Ne,setShowSalesModal:de,setSalesScannerInput:G,navigateToPage:Tt,showToast:Lt,t:n,currentLanguage:s,savedInvoices:j,formatPrice:Mt,calculateDashboardStats:Et});case"sales":return t.jsx(d,{salesInvoice:ye,setSalesInvoice:Ne,showSalesModal:le,setShowSalesModal:de,products:g,customers:x,savedInvoices:j,setSavedInvoices:w,salesScannerInput:J,setSalesScannerInput:G,salesScannerRef:Ft,salesLcdDisplay:_,setSalesLcdDisplay:ee,salesTotalDisplay:re,setSalesTotalDisplay:ie,selectedSalesInvoices:Te,setSelectedSalesInvoices:Ee,salesInvoiceNumberFilter:tt,setSalesInvoiceNumberFilter:nt,salesCustomerFilter:st,setSalesCustomerFilter:at,salesDateFilter:rt,setSalesDateFilter:it,salesPaymentMethodFilter:ot,setSalesPaymentMethodFilter:ct,salesStatusFilter:lt,setSalesStatusFilter:dt,showToast:Lt,formatPrice:Mt,t:n,currentLanguage:s,currentUser:L,isValidScannedCode:At,toggleSelectAll:$t,toggleSelectItem:Ut,deleteSelectedItems:Zt,printInvoice:qt,thermalPrintInvoice:Ot});case"inventory":return t.jsx(u,{products:g,setProducts:v,suppliers:b,categories:P,setCategories:D,showProductModal:me,setShowProductModal:he,showCategoryModal:be,setShowCategoryModal:Se,newProduct:Pe,setNewProduct:De,selectedProducts:Ue,setSelectedProducts:Ze,productSearch:Ye,setProductSearch:Be,selectedCategory:Ke,setSelectedCategory:Ve,selectedStatus:Je,setSelectedStatus:Ge,showToast:Lt,formatPrice:Mt,t:n,currentLanguage:s,currentUser:L,toggleSelectAll:$t,toggleSelectItem:Ut,deleteSelectedItems:Zt,saveProducts:jt,saveCategories:Pt});case"customers":return t.jsx(p,{customers:x,setCustomers:f,showToast:Lt,formatPrice:Mt,t:n,currentLanguage:s,currentUser:L});case"repair":return t.jsx(m,{savedRepairs:I,setSavedRepairs:C,showToast:Lt,formatPrice:Mt,t:n,currentLanguage:s,currentUser:L});default:return t.jsxs("div",{className:"page-not-found",children:[t.jsx("h2",{children:n("pageNotFound","الصفحة غير موجودة")}),t.jsx("button",{onClick:()=>Tt("dashboard"),children:n("backToDashboard","العودة للوحة التحكم")})]})}})()}),t.jsx("div",{className:"toast-container",children:Y.map((e=>t.jsx("div",{className:`toast toast-${e.type}`,children:e.message},e.id)))}),q&&t.jsx("button",{className:"scroll-to-top",onClick:()=>window.scrollTo({top:0,behavior:"smooth"}),children:"↑"})]})},f=({onActivationSuccess:n})=>{const{t:s,currentLanguage:r,getCurrentLanguageConfig:i}=a(),[o,c]=e.useState(""),[l,d]=e.useState(!1),[u,p]=e.useState(""),[m,h]=e.useState(""),[g,x]=e.useState(!1),[f,b]=e.useState(""),S=i();e.useEffect((()=>{const e=v.generateMachineFingerprint();h(e)}),[]),e.useEffect((()=>{const e=e=>{const t=f+e.key.toLowerCase();b(t),(t.includes("reset")||t.includes("test"))&&(x(!0),b("")),setTimeout((()=>{b("")}),3e3)};return document.addEventListener("keypress",e),()=>document.removeEventListener("keypress",e)}),[f]);const j=async()=>{if(o.trim()){d(!0),p("");try{const e=v.activateProgram(o.trim());if(e.success){if("TRIAL"===e.data.type){e.data.trialDays,new Date(e.data.expiryDate).toLocaleDateString("ar-DZ");p("")}n(e.data)}else{let t=e.error;t.includes("البرنامج مفعل بالفعل على هذا الجهاز")?t=s("programAlreadyActivated"):t.includes("تنسيق كود التفعيل غير صحيح")&&(t=s("invalidActivationCodeFormat")),p(t)}}catch(e){p(s("unexpectedActivationError"))}finally{d(!1)}}else p(s("pleaseEnterActivationCode"))};return t.jsxs("div",{className:"activation-overlay",dir:S.direction,children:[t.jsxs("div",{className:"activation-dialog",children:[t.jsx("div",{className:"activation-header",children:t.jsxs("div",{className:"activation-logo",children:[t.jsx("h1",{children:"🏪 iCalDZ"}),t.jsx("p",{children:s("systemDescription","نظام المحاسبة المتكامل")})]})}),t.jsxs("div",{className:"activation-content",children:[t.jsx("h2",{children:s("activationTitle")}),t.jsx("p",{className:"activation-description",children:s("activationDescription")}),t.jsxs("div",{className:"activation-form",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{htmlFor:"activationCode",children:s("activationCodeLabel")}),t.jsx("input",{type:"text",id:"activationCode",value:o,onChange:e=>{let t=e.target.value.toUpperCase();t=t.replace(/[^A-Z0-9-]/g,""),c(t),p("")},onKeyPress:e=>{"Enter"===e.key&&j()},placeholder:s("activationCodePlaceholder"),className:"activation-input "+(u?"error":""),disabled:l,maxLength:50})]}),u&&t.jsx("div",{className:"activation-error",children:t.jsxs("span",{children:["⚠️ ",u]})}),t.jsx("button",{onClick:j,disabled:l||!o.trim(),className:"activation-button",children:l?t.jsxs(t.Fragment,{children:[t.jsx("span",{className:"loading-spinner",children:"⏳"}),s("activating")]}):t.jsxs(t.Fragment,{children:["🔑 ",s("activateProgram")]})}),g&&t.jsxs("button",{onClick:()=>{window.confirm(s("confirmResetActivation"))&&(v.resetActivation(),p(""),c(""),alert(s("resetActivationSuccess")),window.location.reload())},className:"reset-button",title:s("resetActivationTooltip"),children:["🔄 ",s("resetActivation")]})]}),t.jsxs("div",{className:"machine-info",children:[t.jsx("h3",{children:s("deviceInfo")}),t.jsxs("div",{className:"machine-id",children:[t.jsxs("span",{children:[s("deviceId")," "]}),t.jsx("code",{children:m})]}),t.jsxs("p",{className:"machine-note",children:["💡 ",s("deviceNote")]})]}),t.jsx("div",{className:"activation-footer",children:t.jsxs("div",{className:"contact-info",children:[t.jsx("h4",{children:s("getActivationCode")}),t.jsxs("p",{children:["📞 ",s("phone")," +213 551 93 05 89"]}),t.jsxs("p",{children:["📧 ",s("email")," <EMAIL>"]}),t.jsxs("p",{children:["🌐 ",s("website")," www.icodedz.com"]})]})})]})]}),t.jsx("style",{jsx:!0,children:'\n        .activation-overlay {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 10000;\n          font-family: \'Cairo\', sans-serif;\n        }\n\n        .activation-dialog {\n          background: white;\n          border-radius: 20px;\n          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n          max-width: 500px;\n          width: 90%;\n          max-height: 90vh;\n          overflow-y: auto;\n          animation: slideIn 0.5s ease-out;\n        }\n\n        @keyframes slideIn {\n          from {\n            opacity: 0;\n            transform: translateY(-50px) scale(0.9);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0) scale(1);\n          }\n        }\n\n        .activation-header {\n          background: linear-gradient(135deg, #16a085 0%, #2c3e50 100%);\n          color: white;\n          padding: 30px;\n          text-align: center;\n          border-radius: 20px 20px 0 0;\n        }\n\n        .activation-logo h1 {\n          margin: 0;\n          font-size: 2.5rem;\n          font-weight: bold;\n        }\n\n        .activation-logo p {\n          margin: 5px 0 0 0;\n          opacity: 0.9;\n          font-size: 1.1rem;\n        }\n\n        .activation-content {\n          padding: 30px;\n        }\n\n        .activation-content h2 {\n          text-align: center;\n          color: #2c3e50;\n          margin: 0 0 15px 0;\n          font-size: 1.8rem;\n        }\n\n        .activation-description {\n          text-align: center;\n          color: #666;\n          margin-bottom: 30px;\n          line-height: 1.6;\n        }\n\n        /* Language-specific text alignment */\n        [dir="rtl"] .activation-content h2,\n        [dir="rtl"] .activation-description,\n        [dir="rtl"] .contact-info h4,\n        [dir="rtl"] .contact-info p {\n          text-align: right;\n        }\n\n        [dir="ltr"] .activation-content h2,\n        [dir="ltr"] .activation-description,\n        [dir="ltr"] .contact-info h4,\n        [dir="ltr"] .contact-info p {\n          text-align: left;\n        }\n\n        /* Center alignment for titles in all languages */\n        .activation-content h2,\n        .contact-info h4 {\n          text-align: center !important;\n        }\n\n        .form-group {\n          margin-bottom: 20px;\n        }\n\n        .form-group label {\n          display: block;\n          margin-bottom: 8px;\n          font-weight: 600;\n          color: #2c3e50;\n        }\n\n        /* Language-specific label alignment */\n        [dir="rtl"] .form-group label {\n          text-align: right;\n        }\n\n        [dir="ltr"] .form-group label {\n          text-align: left;\n        }\n\n        .activation-input {\n          width: 100%;\n          padding: 15px;\n          border: 2px solid #e0e0e0;\n          border-radius: 10px;\n          font-size: 14px;\n          font-family: \'Courier New\', monospace;\n          text-align: center;\n          transition: all 0.3s ease;\n          box-sizing: border-box;\n        }\n\n        .activation-input:focus {\n          outline: none;\n          border-color: #16a085;\n          box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);\n        }\n\n        .activation-input.error {\n          border-color: #e74c3c;\n          box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);\n        }\n\n        .activation-error {\n          background: #ffe6e6;\n          color: #c0392b;\n          padding: 12px;\n          border-radius: 8px;\n          margin: 15px 0;\n          text-align: center;\n          border: 1px solid #f5b7b1;\n        }\n\n        .activation-button {\n          width: 100%;\n          padding: 15px;\n          background: linear-gradient(135deg, #16a085 0%, #2c3e50 100%);\n          color: white;\n          border: none;\n          border-radius: 10px;\n          font-size: 16px;\n          font-weight: 600;\n          cursor: pointer;\n          transition: all 0.3s ease;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 10px;\n        }\n\n        .activation-button:hover:not(:disabled) {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 25px rgba(22, 160, 133, 0.3);\n        }\n\n        .activation-button:disabled {\n          opacity: 0.6;\n          cursor: not-allowed;\n          transform: none;\n        }\n\n        .reset-button {\n          width: 100%;\n          padding: 12px;\n          background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\n          color: white;\n          border: none;\n          border-radius: 8px;\n          font-size: 14px;\n          font-weight: 500;\n          cursor: pointer;\n          transition: all 0.3s ease;\n          margin-top: 10px;\n          opacity: 0.8;\n        }\n\n        .reset-button:hover {\n          opacity: 1;\n          transform: translateY(-1px);\n          box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);\n        }\n\n        .loading-spinner {\n          animation: spin 1s linear infinite;\n        }\n\n        @keyframes spin {\n          from { transform: rotate(0deg); }\n          to { transform: rotate(360deg); }\n        }\n\n        .machine-info {\n          background: #f8f9fa;\n          padding: 20px;\n          border-radius: 10px;\n          margin: 25px 0;\n          border: 1px solid #e9ecef;\n        }\n\n        .machine-info h3 {\n          margin: 0 0 15px 0;\n          color: #2c3e50;\n          font-size: 1.1rem;\n        }\n\n        /* Language-specific machine info alignment */\n        [dir="rtl"] .machine-info h3,\n        [dir="rtl"] .machine-id,\n        [dir="rtl"] .machine-note {\n          text-align: right;\n        }\n\n        [dir="ltr"] .machine-info h3,\n        [dir="ltr"] .machine-id,\n        [dir="ltr"] .machine-note {\n          text-align: left;\n        }\n\n        .machine-id {\n          background: white;\n          padding: 10px;\n          border-radius: 5px;\n          border: 1px solid #ddd;\n          margin-bottom: 10px;\n        }\n\n        .machine-id code {\n          font-family: \'Courier New\', monospace;\n          color: #16a085;\n          font-weight: bold;\n        }\n\n        .machine-note {\n          font-size: 0.9rem;\n          color: #666;\n          margin: 0;\n          font-style: italic;\n        }\n\n        .activation-footer {\n          border-top: 1px solid #e9ecef;\n          padding-top: 20px;\n          margin-top: 20px;\n        }\n\n        .contact-info h4 {\n          margin: 0 0 15px 0;\n          color: #2c3e50;\n          text-align: center;\n        }\n\n        .contact-info p {\n          margin: 8px 0;\n          color: #666;\n          text-align: center;\n          font-size: 0.9rem;\n        }\n      '})]})};function b(){const{t:n,isLanguageSelected:s,setIsLanguageSelected:r,currentLanguage:o}=a(),[l,d]=e.useState(!1),[u,p]=e.useState(!1),[m,b]=e.useState(!1),[S,j]=e.useState({username:"",password:""}),[w,y]=e.useState(!0);e.useState(!0),e.useState(!0),e.useState(!0),e.useEffect((()=>{(async()=>{try{const e=await v.checkActivation();if(d(e.isActivated),p(!0),e.isActivated){console.log("✅ Application is activated");const e=N();b(e.isLoggedIn)}else console.log("❌ Application needs activation")}catch(e){console.error("Error checking activation:",e),p(!0)}})()}),[]),e.useEffect((()=>(w&&h.initialize(),g&&g.startMonitoring(),window.barcodeShortcutManager||(window.barcodeShortcutManager={isEnabled:!0,isBarcodeActive:!1,setShortcutsEnabled:e=>{window.barcodeShortcutManager.isEnabled=e},checkBarcodeInput:e=>{if(!e||"INPUT"!==e.tagName)return!1;const t=["barcode-input","scanner-input","dashboard-scanner","sales-scanner","edit-scanner","product-barcode"];if(e.className){const n=e.className.toLowerCase();if(t.some((e=>n.includes(e))))return!0}if(e.id){const n=e.id.toLowerCase();if(t.some((e=>n.includes(e))))return!0}if(e.placeholder){const t=e.placeholder.toLowerCase();if(t.includes("barcode")||t.includes("باركود")||t.includes("scanner")||t.includes("مسح"))return!0}return!1}}),()=>{g&&g.stopMonitoring()})),[w]);const N=()=>{try{const e=localStorage.getItem("icaldz-login-status");if(e){const t=JSON.parse(e),n=Date.now();if(t.timestamp&&n-t.timestamp<864e5)return{isLoggedIn:!0,page:t.currentPage||"dashboard"}}}catch(e){console.error("Error checking login status:",e)}return{isLoggedIn:!1,page:"login"}},I=e=>{e.preventDefault();const{username:t,password:s}=S;if([{username:"admin",password:"admin123"},{username:"مدير",password:"123456"},{username:"user",password:"user123"}].some((e=>e.username===t&&e.password===s))){const e={isLoggedIn:!0,username:t,timestamp:Date.now(),currentPage:"dashboard"};localStorage.setItem("icaldz-login-status",JSON.stringify(e)),b(!0),w&&h&&h.playSuccess()}else alert(n("invalidCredentials","اسم المستخدم أو كلمة المرور غير صحيحة")),w&&h&&h.playError()},C=()=>{d(!0)};return s?u&&!l?t.jsx(f,{onActivationSuccess:C}):u?m?t.jsx(c,{children:t.jsx(x,{})}):t.jsx("div",{className:`login-screen lang-${o}`,children:t.jsxs("div",{className:"login-container",children:[t.jsxs("div",{className:"login-header",children:[t.jsx("h1",{children:"iCalDZ"}),t.jsx("p",{children:n("accountingSystem","نظام المحاسبة")})]}),t.jsxs("form",{onSubmit:I,className:"login-form",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("username","اسم المستخدم")}),t.jsx("input",{type:"text",value:S.username,onChange:e=>j((t=>({...t,username:e.target.value}))),placeholder:n("enterUsername","أدخل اسم المستخدم"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("password","كلمة المرور")}),t.jsx("input",{type:"password",value:S.password,onChange:e=>j((t=>({...t,password:e.target.value}))),placeholder:n("enterPassword","أدخل كلمة المرور"),required:!0})]}),t.jsx("button",{type:"submit",className:"login-btn",children:n("login","تسجيل الدخول")})]}),t.jsx("div",{className:"login-footer",children:t.jsx("p",{children:n("defaultCredentials","المستخدم الافتراضي: admin / admin123")})})]})}):t.jsx("div",{className:"loading-screen",children:t.jsxs("div",{className:"loading-content",children:[t.jsx("div",{className:"loading-spinner"}),t.jsx("h2",{children:n("checkingActivation","جاري التحقق من التفعيل...")})]})}):t.jsx(i,{})}function S(){return t.jsx(r,{children:t.jsx(b,{})})}n.createRoot(document.getElementById("root")).render(t.jsx(s.StrictMode,{children:t.jsx(S,{})}));
