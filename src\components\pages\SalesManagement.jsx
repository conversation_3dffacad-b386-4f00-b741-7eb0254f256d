import React, { useState, useRef, useEffect } from 'react';
import { useLanguage } from '../../LanguageContext.jsx';

const SalesManagement = ({ 
  // Props will be passed from main App component
  salesInvoice,
  setSalesInvoice,
  showSalesModal,
  setShowSalesModal,
  products,
  customers,
  savedInvoices,
  setSavedInvoices,
  salesScannerInput,
  setSalesScannerInput,
  salesScannerRef,
  salesLcdDisplay,
  setSalesLcdDisplay,
  salesTotalDisplay,
  setSalesTotalDisplay,
  selectedSalesInvoices,
  setSelectedSalesInvoices,
  salesInvoiceNumberFilter,
  setSalesInvoiceNumberFilter,
  salesCustomerFilter,
  setSalesCustomerFilter,
  salesDateFilter,
  setSalesDateFilter,
  salesPaymentMethodFilter,
  setSalesPaymentMethodFilter,
  salesStatusFilter,
  setSalesStatusFilter,
  showToast,
  formatPrice,
  t,
  currentLanguage,
  currentUser,
  handleSalesScannerInput,
  handleSalesScannerKeyPress,
  isValidScannedCode,
  toggleSelectAll,
  toggleSelectItem,
  deleteSelectedItems,
  printInvoice,
  thermalPrintInvoice
}) => {

  // Filter sales invoices based on current filters
  const filteredSalesInvoices = savedInvoices.filter(invoice => {
    const matchesInvoiceNumber = !salesInvoiceNumberFilter || 
      invoice.invoiceNumber.toLowerCase().includes(salesInvoiceNumberFilter.toLowerCase());
    const matchesCustomer = !salesCustomerFilter || 
      invoice.customerName.toLowerCase().includes(salesCustomerFilter.toLowerCase());
    const matchesDate = !salesDateFilter || invoice.date === salesDateFilter;
    const matchesPaymentMethod = !salesPaymentMethodFilter || 
      invoice.paymentMethod === salesPaymentMethodFilter;
    const matchesStatus = !salesStatusFilter || invoice.status === salesStatusFilter;

    return matchesInvoiceNumber && matchesCustomer && matchesDate && 
           matchesPaymentMethod && matchesStatus;
  });

  const openSalesModal = () => {
    // Reset sales invoice
    const newInvoice = {
      invoiceNumber: 'INV-' + Date.now(),
      date: new Date().toISOString().split('T')[0],
      customerId: 'GUEST',
      customerName: t('walkInCustomer', 'زبون عابر'),
      paymentMethod: 'نقداً',
      items: [],
      total: 0,
      discount: 0,
      tax: 0,
      finalTotal: 0
    };
    
    setSalesInvoice(newInvoice);
    setSalesScannerInput('');
    setSalesLcdDisplay(null);
    setSalesTotalDisplay(null);
    setShowSalesModal(true);
  };

  return (
    <div className="sales-page">
      <div className={`page-header ${currentLanguage !== 'ar' ? 'page-header-ltr-split' : ''}`}>
        <div className="page-title-section">
          <h1>🛒 {t('salesInvoices', 'فواتير المبيعات')}</h1>
        </div>
        <div className="page-actions">
          <button 
            className="btn btn-primary"
            onClick={openSalesModal}
          >
            <span className="btn-icon">➕</span>
            {t('newSalesInvoice', 'فاتورة مبيعات جديدة')}
          </button>
        </div>
      </div>

      {/* Sales Filters */}
      <div className="filters-section">
        <div className="filters-grid">
          <div className="filter-group">
            <label>{t('invoiceNumber', 'رقم الفاتورة')}</label>
            <input
              type="text"
              placeholder={t('searchByInvoiceNumber', 'البحث برقم الفاتورة')}
              value={salesInvoiceNumberFilter}
              onChange={(e) => setSalesInvoiceNumberFilter(e.target.value)}
              className="filter-input"
            />
          </div>

          <div className="filter-group">
            <label>{t('customer', 'العميل')}</label>
            <input
              type="text"
              placeholder={t('searchByCustomer', 'البحث بالعميل')}
              value={salesCustomerFilter}
              onChange={(e) => setSalesCustomerFilter(e.target.value)}
              className="filter-input"
            />
          </div>

          <div className="filter-group">
            <label>{t('date', 'التاريخ')}</label>
            <input
              type="date"
              value={salesDateFilter}
              onChange={(e) => setSalesDateFilter(e.target.value)}
              className="filter-input"
            />
          </div>

          <div className="filter-group">
            <label>{t('paymentMethod', 'طريقة الدفع')}</label>
            <select
              value={salesPaymentMethodFilter}
              onChange={(e) => setSalesPaymentMethodFilter(e.target.value)}
              className="filter-select"
            >
              <option value="">{t('all', 'الكل')}</option>
              <option value="نقداً">{t('cash', 'نقداً')}</option>
              <option value="آجل">{t('credit', 'آجل')}</option>
              <option value="بطاقة">{t('card', 'بطاقة')}</option>
            </select>
          </div>

          <div className="filter-group">
            <label>{t('status', 'الحالة')}</label>
            <select
              value={salesStatusFilter}
              onChange={(e) => setSalesStatusFilter(e.target.value)}
              className="filter-select"
            >
              <option value="">{t('all', 'الكل')}</option>
              <option value="مدفوعة">{t('paid', 'مدفوعة')}</option>
              <option value="غير مدفوعة">{t('unpaid', 'غير مدفوعة')}</option>
              <option value="مرتجعة">{t('returned', 'مرتجعة')}</option>
            </select>
          </div>
        </div>

        <div className="filters-actions">
          <button 
            className="btn btn-secondary"
            onClick={() => {
              setSalesInvoiceNumberFilter('');
              setSalesCustomerFilter('');
              setSalesDateFilter('');
              setSalesPaymentMethodFilter('');
              setSalesStatusFilter('');
            }}
          >
            {t('clearFilters', 'مسح المرشحات')}
          </button>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedSalesInvoices.length > 0 && (
        <div className="bulk-actions">
          <span className="bulk-count">
            {selectedSalesInvoices.length} {t('itemsSelected', 'عنصر محدد')}
          </span>
          <button 
            className="btn btn-danger"
            onClick={() => deleteSelectedItems('sales')}
          >
            {t('deleteSelected', 'حذف المحدد')}
          </button>
        </div>
      )}

      {/* Sales Invoices Table */}
      <div className="table-container">
        <table className="data-table">
          <thead>
            <tr>
              <th>
                <input
                  type="checkbox"
                  checked={selectedSalesInvoices.length === filteredSalesInvoices.length && filteredSalesInvoices.length > 0}
                  onChange={() => toggleSelectAll('sales', filteredSalesInvoices)}
                />
              </th>
              <th>{t('invoiceNumber', 'رقم الفاتورة')}</th>
              <th>{t('customer', 'العميل')}</th>
              <th>{t('date', 'التاريخ')}</th>
              <th>{t('total', 'الإجمالي')}</th>
              <th>{t('paymentMethod', 'طريقة الدفع')}</th>
              <th>{t('status', 'الحالة')}</th>
              <th>{t('actions', 'الإجراءات')}</th>
            </tr>
          </thead>
          <tbody>
            {filteredSalesInvoices.map(invoice => (
              <tr key={invoice.id}>
                <td>
                  <input
                    type="checkbox"
                    checked={selectedSalesInvoices.includes(invoice.id)}
                    onChange={() => toggleSelectItem('sales', invoice.id)}
                  />
                </td>
                <td>{invoice.invoiceNumber}</td>
                <td>{invoice.customerName}</td>
                <td>{new Date(invoice.date).toLocaleDateString('ar-DZ')}</td>
                <td>{formatPrice(invoice.finalTotal)}</td>
                <td>{invoice.paymentMethod}</td>
                <td>
                  <span className={`status-badge status-${invoice.status === 'مدفوعة' ? 'paid' : 'unpaid'}`}>
                    {invoice.status || 'مدفوعة'}
                  </span>
                </td>
                <td>
                  <div className="action-buttons">
                    <button
                      className="btn btn-sm btn-info"
                      onClick={() => printInvoice(invoice)}
                      title={t('print', 'طباعة')}
                    >
                      🖨️
                    </button>
                    <button
                      className="btn btn-sm btn-success"
                      onClick={() => thermalPrintInvoice(invoice)}
                      title={t('thermalPrint', 'طباعة حرارية')}
                    >
                      🎫
                    </button>
                    {(currentUser.role === 'مدير' || currentUser.role === 'admin') && (
                      <button
                        className="btn btn-sm btn-warning"
                        onClick={() => {
                          // Edit invoice functionality
                          setSalesInvoice(invoice);
                          setShowSalesModal(true);
                        }}
                        title={t('edit', 'تعديل')}
                      >
                        ✏️
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Sales Statistics */}
      <div className="sales-stats">
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">📊</div>
            <div className="stat-content">
              <h3>{t('totalSalesInvoices', 'إجمالي فواتير المبيعات')}</h3>
              <div className="stat-value">{filteredSalesInvoices.length}</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">💰</div>
            <div className="stat-content">
              <h3>{t('totalSalesAmount', 'إجمالي مبلغ المبيعات')}</h3>
              <div className="stat-value">
                {formatPrice(filteredSalesInvoices.reduce((sum, inv) => sum + inv.finalTotal, 0))}
              </div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">💳</div>
            <div className="stat-content">
              <h3>{t('cashSales', 'المبيعات النقدية')}</h3>
              <div className="stat-value">
                {formatPrice(filteredSalesInvoices
                  .filter(inv => inv.paymentMethod === 'نقداً')
                  .reduce((sum, inv) => sum + inv.finalTotal, 0))}
              </div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">📅</div>
            <div className="stat-content">
              <h3>{t('creditSales', 'المبيعات الآجلة')}</h3>
              <div className="stat-value">
                {formatPrice(filteredSalesInvoices
                  .filter(inv => inv.paymentMethod === 'آجل')
                  .reduce((sum, inv) => sum + inv.finalTotal, 0))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesManagement;
