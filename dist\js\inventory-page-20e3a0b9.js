import{j as e}from"./react-vendor-03c8a839.js";import"./i18n-121a6c54.js";const t=({products:t,setProducts:s,suppliers:c,categories:a,setCategories:l,showProductModal:n,setShowProductModal:i,showCategoryModal:r,setShowCategoryModal:d,newProduct:o,setNewProduct:h,newCategory:u,setNewCategory:x,editingCategory:j,setEditingCategory:m,selectedProducts:g,setSelectedProducts:v,productSearch:p,setProductSearch:N,selectedCategory:y,setSelectedCategory:b,selectedStatus:k,setSelectedStatus:S,showToast:C,formatPrice:P,t:w,currentLanguage:f,currentUser:q,toggleSelectAll:L,toggleSelectItem:I,deleteSelectedItems:M,saveProducts:O,saveCategories:A})=>{const E=t.filter((e=>{const t=!p||e.name.toLowerCase().includes(p.toLowerCase())||e.barcode.toLowerCase().includes(p.toLowerCase()),s=!y||e.category===y,c=!k||"low-stock"===k&&e.quantity<=e.minStock||"out-of-stock"===k&&0===e.quantity||"in-stock"===k&&e.quantity>0;return t&&s&&c}));return e.jsxs("div",{className:"inventory-page",children:[e.jsxs("div",{className:"page-header "+("ar"!==f?"page-header-ltr-split":""),children:[e.jsx("div",{className:"page-title-section",children:e.jsxs("h1",{children:["📋 ",w("inventory","المخزون")]})}),e.jsxs("div",{className:"page-actions",children:[e.jsxs("button",{className:"btn btn-primary",onClick:()=>{h({id:"",name:"",barcode:"",category:"",buyPrice:0,sellPrice:0,quantity:0,minStock:5,supplier:"",description:""}),i(!0)},children:[e.jsx("span",{className:"btn-icon",children:"➕"}),w("newProduct","منتج جديد")]}),e.jsxs("button",{className:"btn btn-secondary",onClick:()=>d(!0),children:[e.jsx("span",{className:"btn-icon",children:"🏷️"}),w("manageCategories","إدارة الفئات")]})]})]}),e.jsxs("div",{className:"filters-section",children:[e.jsxs("div",{className:"filters-grid",children:[e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:w("search","البحث")}),e.jsx("input",{type:"text",placeholder:w("searchProducts","البحث في المنتجات"),value:p,onChange:e=>N(e.target.value),className:"filter-input"})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:w("category","الفئة")}),e.jsxs("select",{value:y,onChange:e=>b(e.target.value),className:"filter-select",children:[e.jsx("option",{value:"",children:w("allCategories","جميع الفئات")}),a.map((t=>e.jsx("option",{value:t,children:t},t)))]})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:w("status","الحالة")}),e.jsxs("select",{value:k,onChange:e=>S(e.target.value),className:"filter-select",children:[e.jsx("option",{value:"",children:w("allProducts","جميع المنتجات")}),e.jsx("option",{value:"in-stock",children:w("inStock","متوفر")}),e.jsx("option",{value:"low-stock",children:w("lowStock","مخزون قليل")}),e.jsx("option",{value:"out-of-stock",children:w("outOfStock","نفد المخزون")})]})]})]}),e.jsx("div",{className:"filters-actions",children:e.jsx("button",{className:"btn btn-secondary",onClick:()=>{N(""),b(""),S("")},children:w("clearFilters","مسح المرشحات")})})]}),g.length>0&&e.jsxs("div",{className:"bulk-actions",children:[e.jsxs("span",{className:"bulk-count",children:[g.length," ",w("itemsSelected","عنصر محدد")]}),e.jsx("button",{className:"btn btn-danger",onClick:()=>M("products"),children:w("deleteSelected","حذف المحدد")})]}),e.jsx("div",{className:"table-container",children:e.jsxs("table",{className:"data-table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:e.jsx("input",{type:"checkbox",checked:g.length===E.length&&E.length>0,onChange:()=>L("products",E)})}),e.jsx("th",{children:w("name","الاسم")}),e.jsx("th",{children:w("barcode","الباركود")}),e.jsx("th",{children:w("category","الفئة")}),e.jsx("th",{children:w("supplier","المورد")}),e.jsx("th",{children:w("buyPrice","سعر الشراء")}),e.jsx("th",{children:w("sellPrice","سعر البيع")}),e.jsx("th",{children:w("quantity","الكمية")}),e.jsx("th",{children:w("minStock","الحد الأدنى")}),e.jsx("th",{children:w("status","الحالة")}),e.jsx("th",{children:w("actions","الإجراءات")})]})}),e.jsx("tbody",{children:E.map((t=>e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("input",{type:"checkbox",checked:g.includes(t.id),onChange:()=>I("products",t.id)})}),e.jsx("td",{children:t.name}),e.jsx("td",{children:t.barcode}),e.jsx("td",{children:t.category}),e.jsx("td",{children:t.supplier||"-"}),e.jsx("td",{children:P(t.buyPrice)}),e.jsx("td",{children:P(t.sellPrice)}),e.jsx("td",{children:e.jsx("span",{className:"quantity "+(t.quantity<=t.minStock?"low-stock":""),children:t.quantity})}),e.jsx("td",{children:t.minStock}),e.jsx("td",{children:e.jsx("span",{className:"status-badge "+(0===t.quantity?"status-out-of-stock":t.quantity<=t.minStock?"status-low-stock":"status-in-stock"),children:0===t.quantity?w("outOfStock","نفد المخزون"):t.quantity<=t.minStock?w("lowStock","مخزون قليل"):w("inStock","متوفر")})}),e.jsx("td",{children:e.jsxs("div",{className:"action-buttons",children:[e.jsx("button",{className:"btn btn-sm btn-warning",onClick:()=>(e=>{h(e),i(!0)})(t),title:w("edit","تعديل"),children:"✏️"}),("مدير"===q.role||"admin"===q.role)&&e.jsx("button",{className:"btn btn-sm btn-danger",onClick:()=>M("products",[t.id]),title:w("delete","حذف"),children:"🗑️"})]})})]},t.id)))})]})}),e.jsx("div",{className:"inventory-stats",children:e.jsxs("div",{className:"stats-grid",children:[e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-icon",children:"📦"}),e.jsxs("div",{className:"stat-content",children:[e.jsx("h3",{children:w("totalProducts","إجمالي المنتجات")}),e.jsx("div",{className:"stat-value",children:E.length})]})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-icon",children:"💰"}),e.jsxs("div",{className:"stat-content",children:[e.jsx("h3",{children:w("totalInventoryValue","قيمة المخزون الإجمالية")}),e.jsx("div",{className:"stat-value",children:P(E.reduce(((e,t)=>e+t.quantity*t.buyPrice),0))})]})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-icon",children:"⚠️"}),e.jsxs("div",{className:"stat-content",children:[e.jsx("h3",{children:w("lowStockProducts","منتجات قليلة المخزون")}),e.jsx("div",{className:"stat-value",children:E.filter((e=>e.quantity<=e.minStock&&e.quantity>0)).length})]})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-icon",children:"❌"}),e.jsxs("div",{className:"stat-content",children:[e.jsx("h3",{children:w("outOfStockProducts","منتجات نفد مخزونها")}),e.jsx("div",{className:"stat-value",children:E.filter((e=>0===e.quantity)).length})]})]})]})})]})};export{t as I};
