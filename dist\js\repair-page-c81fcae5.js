import{j as e}from"./react-vendor-03c8a839.js";const i=({repairManagement:i,currentLanguage:s,t:a,formatPrice:r})=>{const{showNewRepairModal:l,setShowNewRepairModal:c,showRepairCompletionModal:n,setShowRepairCompletionModal:t,showClientRecoveryModal:d,setShowClientRecoveryModal:o,showRepairInfoModal:m,setShowRepairInfoModal:h,showRepairActionsModal:p,setShowRepairActionsModal:x,selectedRepairForActions:j,setSelectedRepairForActions:v,newRepair:u,setNewRepair:N,repairClientFilter:R,setRepairClientFilter:g,repairDeviceFilter:b,setRepairDeviceFilter:y,repairStatusFilter:f,setRepairStatusFilter:C,repairDateFilter:w,setRepairDateFilter:F,filteredRepairs:P,generateNextRepairId:S,generateRepairBarcode:D,resetNewRepairForm:M,printRepairQRCode:k,printRepairBonPour:I,handleCreateNewRepair:A,handleCompleteRepair:B}=i;return e.jsxs("div",{className:`repair-page lang-${s}`,children:[e.jsx("div",{className:"page-header "+("ar"!==s?"page-header-ltr-split":""),children:e.jsxs("div",{className:"page-title-section",children:[e.jsxs("h1",{children:["🛠️ ","ar"===s?"إدارة الإصلاح":"fr"===s?"Gestion des Réparations":"Repair Management"]}),e.jsx("p",{children:"ar"===s?"سير عمل الإصلاح":"fr"===s?"Flux de travail des réparations":"Repair Workflow"})]})}),e.jsx("div",{className:"repair-actions-section centered-actions",children:e.jsxs("div",{className:"repair-actions-grid centered-row",children:[e.jsx("div",{className:"repair-action-card new-repair",children:e.jsxs("button",{className:"repair-action-btn",onClick:()=>{const e=S(),i=D(e);N({...u,id:e,barcode:i,createdAt:(new Date).toISOString()}),c(!0)},children:[e.jsx("div",{className:"repair-action-icon",children:"🆕"}),e.jsx("div",{className:"repair-action-title",children:a("newBonPour","بون بور جديد")}),e.jsx("div",{className:"repair-action-desc",children:a("createNewRepairTicket","إنشاء تذكرة إصلاح جديدة")})]})}),e.jsx("div",{className:"repair-action-card complete-repair",children:e.jsxs("button",{className:"repair-action-btn",onClick:()=>t(!0),children:[e.jsx("div",{className:"repair-action-icon",children:"✅"}),e.jsx("div",{className:"repair-action-title",children:a("repairCompleted","إصلاح مكتمل")}),e.jsx("div",{className:"repair-action-desc",children:a("markRepairAsCompleted","تحديد الإصلاح كمكتمل")})]})}),e.jsx("div",{className:"repair-action-card client-recovery",children:e.jsxs("button",{className:"repair-action-btn",onClick:()=>o(!0),children:[e.jsx("div",{className:"repair-action-icon",children:"📱"}),e.jsx("div",{className:"repair-action-title",children:a("clientRecovery","استلام من العميل")}),e.jsx("div",{className:"repair-action-desc",children:a("clientDevicePickup","استلام الجهاز من العميل")})]})})]})}),e.jsx("div",{className:"repair-filters-section",children:e.jsxs("div",{className:"filters-row",children:[e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:a("filterByClient","تصفية حسب العميل")}),e.jsx("input",{type:"text",value:R,onChange:e=>g(e.target.value),placeholder:a("clientName","اسم العميل")})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:a("filterByDevice","تصفية حسب الجهاز")}),e.jsx("input",{type:"text",value:b,onChange:e=>y(e.target.value),placeholder:a("deviceName","اسم الجهاز")})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:a("filterByStatus","تصفية حسب الحالة")}),e.jsxs("select",{value:f,onChange:e=>C(e.target.value),children:[e.jsx("option",{value:"",children:a("allStatuses","جميع الحالات")}),e.jsx("option",{value:"In Process",children:a("inProcess","قيد المعالجة")}),e.jsx("option",{value:"Waiting for Client",children:a("waitingForClient","في انتظار العميل")}),e.jsx("option",{value:"Done",children:a("done","مكتمل")}),e.jsx("option",{value:"Not Success",children:a("notSuccess","غير ناجح")})]})]}),e.jsxs("div",{className:"filter-group",children:[e.jsx("label",{children:a("filterByDate","تصفية حسب التاريخ")}),e.jsx("input",{type:"date",value:w,onChange:e=>F(e.target.value)})]}),e.jsx("div",{className:"filter-actions",children:e.jsxs("button",{className:"btn btn-secondary",onClick:()=>{g(""),y(""),C(""),F("")},children:["🗑️ ",a("clearFilters","مسح المرشحات")]})})]})}),e.jsxs("div",{className:"repair-summary-cards-top",children:[e.jsxs("div",{className:"summary-card-item total-repairs",children:[e.jsx("div",{className:"summary-icon",children:"🛠️"}),e.jsxs("div",{className:"summary-content",children:[e.jsx("div",{className:"summary-number",children:P.length}),e.jsx("div",{className:"summary-label",children:a("totalRepairs","إجمالي الإصلاحات")})]})]}),e.jsxs("div",{className:"summary-card-item completed-repairs",children:[e.jsx("div",{className:"summary-icon",children:"✅"}),e.jsxs("div",{className:"summary-content",children:[e.jsx("div",{className:"summary-number",children:P.filter((e=>"Done"===e.situation)).length}),e.jsx("div",{className:"summary-label",children:a("completedRepairs","الإصلاحات المكتملة")})]})]}),e.jsxs("div",{className:"summary-card-item pending-repairs",children:[e.jsx("div",{className:"summary-icon",children:"⏳"}),e.jsxs("div",{className:"summary-content",children:[e.jsx("div",{className:"summary-number",children:P.filter((e=>"In Process"===e.situation||"Waiting for Client"===e.situation)).length}),e.jsx("div",{className:"summary-label",children:a("pendingRepairs","الإصلاحات المعلقة")})]})]}),e.jsxs("div",{className:"summary-card-item repair-revenue",children:[e.jsx("div",{className:"summary-icon",children:"💰"}),e.jsxs("div",{className:"summary-content",children:[e.jsx("div",{className:"summary-number",children:r(P.reduce(((e,i)=>e+(parseFloat(i.repairPrice)||0)-(parseFloat(i.partsPrice)||0)),0))}),e.jsx("div",{className:"summary-label",children:a("totalRepairRevenue","إجمالي إيرادات الإصلاح")})]})]})]}),e.jsx("div",{className:"repair-table-section",children:e.jsx("div",{className:"table-container",children:e.jsxs("table",{className:"data-table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:a("qrCode","رمز QR")}),e.jsx("th",{children:a("clientInfo","معلومات العميل")}),e.jsx("th",{children:a("phoneNumber","رقم الهاتف")}),e.jsx("th",{children:a("device","الجهاز")}),e.jsx("th",{children:a("problem","المشكلة")}),e.jsx("th",{children:a("repairPrice","سعر الإصلاح")}),e.jsx("th",{children:a("totalPrice","السعر الإجمالي")}),e.jsx("th",{children:a("status","الحالة")}),e.jsx("th",{children:a("actions","الإجراءات")})]})}),e.jsxs("tbody",{children:[P.map((i=>e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"qr-cell",onClick:()=>k(i),title:a("clickToPrintQR","اضغط لطباعة رمز QR"),children:[e.jsx("div",{className:"qr-icon",children:"📱"}),e.jsx("div",{className:"repair-id",children:i.id})]})}),e.jsx("td",{children:e.jsx("div",{className:"client-info",children:e.jsx("div",{className:"client-name",children:i.clientName})})}),e.jsx("td",{children:e.jsx("div",{className:"phone-info",children:e.jsx("div",{className:"phone-number",children:i.clientPhone||a("noPhone","لا يوجد هاتف")})})}),e.jsx("td",{children:e.jsx("div",{className:"device-info",children:e.jsx("div",{className:"device-name",children:i.deviceName})})}),e.jsx("td",{children:e.jsx("div",{className:"problem-info",children:e.jsx("div",{className:"problem-text",children:i.problem})})}),e.jsx("td",{children:e.jsx("div",{className:"price-cell",children:e.jsx("div",{className:"price-amount",children:r(parseFloat(i.repairPrice)||0)})})}),e.jsx("td",{children:e.jsx("div",{className:"price-cell",children:e.jsx("div",{className:"total-price-amount",children:i.repairFailed?r(parseFloat(i.repairPrice)||0):r((parseFloat(i.repairPrice)||0)-(parseFloat(i.partsPrice)||0))})})}),e.jsx("td",{children:e.jsx("span",{className:"status-badge "+("In Process"===i.situation?"status-in-process":"Waiting for Client"===i.situation?"status-waiting-for-client":"Done"===i.situation?"status-done":"status-not-success"),children:"In Process"===i.situation?a("inProcess","قيد المعالجة"):"Waiting for Client"===i.situation?a("waitingForClient","في انتظار العميل"):"Done"===i.situation?a("done","مكتمل"):a("notSuccess","غير ناجح")})}),e.jsx("td",{children:e.jsx("div",{className:"repair-actions-cell",children:e.jsxs("button",{className:"repair-actions-btn modern-actions-btn",onClick:()=>{v(i),x(!0)},title:a("actions","الإجراءات"),children:[e.jsx("span",{className:"actions-icon",children:"⋮"}),e.jsx("span",{className:"actions-text",children:a("actions","الإجراءات")})]})})})]},i.id))),0===P.length&&e.jsx("tr",{children:e.jsx("td",{colSpan:"9",style:{textAlign:"center",padding:"40px"},children:e.jsxs("div",{className:"no-data-message",children:[e.jsx("div",{className:"no-data-icon",children:"🛠️"}),e.jsx("h3",{children:a("noRepairsFound","لا توجد إصلاحات")}),e.jsx("p",{children:a("createFirstRepair",'قم بإنشاء أول إصلاح باستخدام زر "بون بور جديد"')})]})})})]})]})})})]})};export{i as R};
