const e=new class{constructor(){this.isInitialized=!1,this.startTime=Date.now(),this.cleanupIntervals=new Set,this.activeTimeouts=new Set,this.activeIntervals=new Set,this.eventListeners=new Map,this.domObservers=new Set,this.memoryStats={totalCleanups:0,timeoutsCleared:0,intervalsCleared:0,listenersRemoved:0,lastCleanup:Date.now(),memoryUsage:0},this.systemHealth={isHealthy:!0,lastActivity:Date.now(),errorCount:0,warningCount:0,criticalErrors:[]},this.init()}init(){try{this.setupPeriodicCleanup(),this.setupMemoryMonitoring(),this.setupEmergencyRecovery(),this.setupHealthMonitoring(),this.isInitialized=!0,console.log("🧠 Memory Manager: System initialized successfully")}catch(e){console.error("🧠 Memory Manager: Initialization failed",e),this.systemHealth.criticalErrors.push({error:e.message,timestamp:Date.now(),context:"initialization"})}}setupPeriodicCleanup(){const e=setInterval((()=>{this.performMainCleanup()}),3e5),t=setInterval((()=>{this.performDeepCleanup()}),18e5),a=setInterval((()=>{this.performEmergencyCleanup()}),36e5);this.cleanupIntervals.add(e),this.cleanupIntervals.add(t),this.cleanupIntervals.add(a)}setupMemoryMonitoring(){if(performance&&performance.memory){const e=setInterval((()=>{this.checkMemoryUsage()}),12e4);this.cleanupIntervals.add(e)}}setupEmergencyRecovery(){const e=setInterval((()=>{this.checkForSystemBlocking()}),6e5);this.cleanupIntervals.add(e)}setupHealthMonitoring(){const e=setInterval((()=>{this.updateSystemHealth()}),3e5);this.cleanupIntervals.add(e)}registerTimeout(e,t="unknown"){return this.activeTimeouts.add({id:e,context:t,created:Date.now()}),e}registerInterval(e,t="unknown"){return this.activeIntervals.add({id:e,context:t,created:Date.now()}),e}registerEventListener(e,t,a,r="unknown"){const s=`${e.constructor.name}-${t}-${r}`;this.eventListeners.set(s,{element:e,event:t,handler:a,context:r,created:Date.now()})}clearTimeout(e){clearTimeout(e),this.activeTimeouts.forEach((t=>{t.id===e&&(this.activeTimeouts.delete(t),this.memoryStats.timeoutsCleared++)}))}clearInterval(e){clearInterval(e),this.activeIntervals.forEach((t=>{t.id===e&&(this.activeIntervals.delete(t),this.memoryStats.intervalsCleared++)}))}performMainCleanup(){console.log("🧠 Memory Manager: Performing main cleanup...");try{this.clearOldTimeouts(36e5),this.clearOldIntervals(72e5),this.cleanupDOMElements(),this.clearScannerTimeouts(),this.memoryStats.totalCleanups++,this.memoryStats.lastCleanup=Date.now(),console.log("🧠 Memory Manager: Main cleanup completed")}catch(e){console.error("🧠 Memory Manager: Main cleanup failed",e),this.systemHealth.errorCount++}}performDeepCleanup(){console.log("🧠 Memory Manager: Performing deep cleanup...");try{this.clearAllTimeouts(),this.removeOldEventListeners(),this.forceGarbageCollection(),this.resetScannerSystem(),console.log("🧠 Memory Manager: Deep cleanup completed")}catch(e){console.error("🧠 Memory Manager: Deep cleanup failed",e),this.systemHealth.errorCount++}}performEmergencyCleanup(){console.log("🧠 Memory Manager: Performing emergency cleanup...");try{this.clearAllTimeouts(),this.clearAllIntervals(),this.removeAllEventListeners(),this.resetAllSystems();for(let e=0;e<3;e++)setTimeout((()=>this.forceGarbageCollection()),1e3*e);console.log("🧠 Memory Manager: Emergency cleanup completed")}catch(e){console.error("🧠 Memory Manager: Emergency cleanup failed",e),this.systemHealth.criticalErrors.push({error:e.message,timestamp:Date.now(),context:"emergency_cleanup"})}}clearOldTimeouts(e){const t=Date.now();this.activeTimeouts.forEach((a=>{t-a.created>e&&(clearTimeout(a.id),this.activeTimeouts.delete(a),this.memoryStats.timeoutsCleared++)}))}clearOldIntervals(e){const t=Date.now();this.activeIntervals.forEach((a=>{t-a.created>e&&(clearInterval(a.id),this.activeIntervals.delete(a),this.memoryStats.intervalsCleared++)}))}clearAllTimeouts(){this.activeTimeouts.forEach((e=>{clearTimeout(e.id),this.memoryStats.timeoutsCleared++})),this.activeTimeouts.clear()}clearAllIntervals(){this.activeIntervals.forEach((e=>{clearInterval(e.id),this.memoryStats.intervalsCleared++})),this.activeIntervals.clear()}removeOldEventListeners(){const e=Date.now();this.eventListeners.forEach(((t,a)=>{if(e-t.created>72e5)try{t.element.removeEventListener(t.event,t.handler),this.eventListeners.delete(a),this.memoryStats.listenersRemoved++}catch(r){console.warn("🧠 Memory Manager: Failed to remove event listener",r)}}))}removeAllEventListeners(){this.eventListeners.forEach(((e,t)=>{try{e.element.removeEventListener(e.event,e.handler),this.memoryStats.listenersRemoved++}catch(a){console.warn("🧠 Memory Manager: Failed to remove event listener",a)}})),this.eventListeners.clear()}cleanupDOMElements(){document.querySelectorAll('[data-cleanup="true"]').forEach((e=>{try{e.remove()}catch(t){console.warn("🧠 Memory Manager: Failed to remove DOM element",t)}}));document.querySelectorAll('iframe[style*="position: absolute"]').forEach((e=>{try{"-9999px"===e.style.left&&e.remove()}catch(t){console.warn("🧠 Memory Manager: Failed to remove print iframe",t)}}))}clearScannerTimeouts(){["dashboardScannerTimeout","salesScannerTimeout","salesScannerValidationTimeout","editScannerValidationTimeout","productScannerTimeout"].forEach((e=>{window[e]&&(clearTimeout(window[e]),window[e]=null)}))}resetScannerSystem(){window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0)),this.clearScannerTimeouts()}resetAllSystems(){if(this.resetScannerSystem(),window.KeyboardShortcuts&&window.KeyboardShortcuts.setEnabled(!0),window.SoundManager)try{window.SoundManager.cleanup()}catch(e){console.warn("🧠 Memory Manager: Failed to cleanup sound manager",e)}}forceGarbageCollection(){if(window.gc&&"function"==typeof window.gc)try{window.gc(),console.log("🧠 Memory Manager: Forced garbage collection")}catch(e){console.warn("🧠 Memory Manager: Garbage collection not available",e)}}checkMemoryUsage(){if(performance&&performance.memory){const e=performance.memory;this.memoryStats.memoryUsage=e.usedJSHeapSize;const t=104857600;e.usedJSHeapSize>t&&(console.warn("🧠 Memory Manager: High memory usage detected",{used:Math.round(e.usedJSHeapSize/1024/1024)+"MB",total:Math.round(e.totalJSHeapSize/1024/1024)+"MB"}),this.performEmergencyCleanup())}}checkForSystemBlocking(){Date.now()-this.systemHealth.lastActivity>18e5&&(console.warn("🧠 Memory Manager: Potential system blocking detected"),this.performEmergencyCleanup(),this.systemHealth.warningCount++)}updateSystemHealth(){const e=Date.now(),t=e-this.startTime;this.systemHealth.lastActivity=e,this.systemHealth.isHealthy=this.systemHealth.errorCount<10,t%36e5<3e5&&console.log("🧠 Memory Manager: System health status",{uptime:Math.round(t/1e3/60)+" minutes",isHealthy:this.systemHealth.isHealthy,errorCount:this.systemHealth.errorCount,memoryUsage:Math.round(this.memoryStats.memoryUsage/1024/1024)+"MB",totalCleanups:this.memoryStats.totalCleanups})}getStatus(){return{initialized:this.isInitialized,uptime:Date.now()-this.startTime,memoryStats:this.memoryStats,systemHealth:this.systemHealth,activeTimeouts:this.activeTimeouts.size,activeIntervals:this.activeIntervals.size,eventListeners:this.eventListeners.size}}destroy(){console.log("🧠 Memory Manager: Shutting down..."),this.cleanupIntervals.forEach((e=>clearInterval(e))),this.cleanupIntervals.clear(),this.performEmergencyCleanup(),this.isInitialized=!1,console.log("🧠 Memory Manager: Shutdown completed")}};export{e as m};
