import React, { useState, useRef, useEffect } from 'react';
import { useLanguage } from '../../LanguageContext.jsx';

const Dashboard = ({ 
  dashboardScannerInput,
  setDashboardScannerInput,
  dashboardScannerRef,
  dashboardLcdDisplay,
  setDashboardLcdDisplay,
  dashboardTotalDisplay,
  setDashboardTotalDisplay,
  products,
  handleDashboardScannerInput,
  handleDashboardScannerKeyPress,
  isValidScannedCode,
  setSalesInvoice,
  setShowSalesModal,
  setSalesScannerInput,
  navigateToPage,
  showToast,
  t,
  currentLanguage,
  savedInvoices,
  formatPrice,
  calculateDashboardStats
}) => {
  
  const dashboardStats = calculateDashboardStats();

  return (
    <div className="dashboard">
      <div className={`page-header ${currentLanguage !== 'ar' ? 'page-header-ltr-split' : ''}`}>
        <div className="page-title-section">
          <h1>🏠 {t('dashboard', 'لوحة التحكم')}</h1>
        </div>
      </div>

      {/* Combined Scanner and LCD Frame - ABOVE the 3 buttons */}
      <div className="dashboard-scanner-lcd-unified">
        <div className="unified-frame">
          {/* Scanner Section */}
          <div className="scanner-section">
            <h3>📷 {t('scanBarcode', 'مسح الباركود')} - <span className="scanner-status-active">{t('active', 'نشط')}</span></h3>
            <div className="barcode-input-container">
              <span className="barcode-icon">📷</span>
              <input
                type="text"
                placeholder={t('scanBarcodeToAddProduct', 'امسح الباركود - اضغط Enter لفتح الفاتورة')}
                value={dashboardScannerInput}
                onChange={handleDashboardScannerInput}
                onKeyDown={handleDashboardScannerKeyPress}
                onFocus={() => {
                  // 🔧 BARCODE SCANNER FIX: Disable shortcuts when barcode input is focused
                  if (window.barcodeShortcutManager) {
                    window.barcodeShortcutManager.isBarcodeActive = true;
                    window.barcodeShortcutManager.setShortcutsEnabled(false);
                    console.log('🔧 BARCODE FIX: Dashboard scanner focused - shortcuts disabled');
                  }
                }}
                onBlur={() => {
                  // 🔧 BARCODE SCANNER FIX: Re-enable shortcuts when barcode input loses focus
                  setTimeout(() => {
                    if (window.barcodeShortcutManager && !window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)) {
                      window.barcodeShortcutManager.isBarcodeActive = false;
                      window.barcodeShortcutManager.setShortcutsEnabled(true);
                      console.log('🔧 BARCODE FIX: Dashboard scanner blurred - shortcuts re-enabled');
                    }
                  }, 100);
                }}
                className="barcode-input"
                ref={dashboardScannerRef}
                autoFocus
              />
            </div>
            <div className="barcode-actions">
              <button
                type="button"
                className="btn btn-success btn-sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log('🏠 Dashboard: Manual button click - Opening invoice');
                  const barcode = dashboardScannerInput.trim();
                  if (barcode && isValidScannedCode(barcode)) {
                    const foundProduct = products.find(p => p.barcode === barcode && barcode.trim() !== '');
                    if (foundProduct) {
                      console.log('🏠 Dashboard: Opening sales invoice with product:', foundProduct.name);

                      // Clear dashboard scanner input only
                      setDashboardScannerInput('');
                      // Keep LCD display for total final display

                      // Create new invoice object
                      const newInvoice = {
                        invoiceNumber: 'INV-' + Date.now(),
                        date: new Date().toISOString().split('T')[0],
                        customerId: 'GUEST',
                        customerName: t('walkInCustomer', 'زبون عابر'),
                        paymentMethod: 'نقداً',
                        items: [],
                        total: 0,
                        discount: 0,
                        tax: 0,
                        finalTotal: 0
                      };

                      // Clear any sales scanner states
                      setSalesScannerInput('');

                      // Reset sales invoice completely and open modal
                      setSalesInvoice(newInvoice);
                      setShowSalesModal(true);

                      // Add product to invoice after state has been set
                      setTimeout(() => {
                        console.log('🏠 Dashboard: Adding product to new sales invoice:', foundProduct.name);

                        // Create new item for the scanned product
                        const newItem = {
                          productId: foundProduct.id,
                          productName: foundProduct.name,
                          quantity: 1,
                          price: foundProduct.sellPrice || foundProduct.price,
                          total: foundProduct.sellPrice || foundProduct.price
                        };

                        // Update sales invoice with the new item
                        setSalesInvoice(prev => {
                          const updatedInvoice = {
                            ...prev,
                            items: [...prev.items, newItem]
                          };
                          
                          // Calculate totals
                          const total = updatedInvoice.items.reduce((sum, item) => sum + item.total, 0);
                          updatedInvoice.total = total;
                          updatedInvoice.finalTotal = total - updatedInvoice.discount + updatedInvoice.tax;
                          
                          return updatedInvoice;
                        });

                        showToast(`✅ ${t('productAddedToInvoice', 'تم إضافة المنتج للفاتورة')}: ${foundProduct.name}`, 'success', 2000);
                      }, 100);
                    } else {
                      showToast(`❌ ${t('productNotFound', 'المنتج غير موجود')}: ${barcode}`, 'error', 3000);
                    }
                  } else {
                    showToast(`⚠️ ${t('invalidBarcode', 'باركود غير صحيح')}`, 'warning', 2000);
                  }
                }}
              >
                {t('openInvoice', 'فتح الفاتورة')}
              </button>
            </div>
          </div>

          {/* LCD Display Section */}
          <div className="lcd-section">
            <h3>💰 {t('lcdDisplay', 'شاشة العرض')}</h3>
            <div className="lcd-display">
              <div className="lcd-screen">
                {dashboardLcdDisplay ? (
                  <div className="lcd-content">
                    <div className="lcd-product-name">{dashboardLcdDisplay.name}</div>
                    <div className="lcd-product-price">{formatPrice(dashboardLcdDisplay.price)}</div>
                  </div>
                ) : dashboardTotalDisplay ? (
                  <div className="lcd-total-content">
                    <div className="lcd-total-label">{t('invoiceTotal', 'إجمالي الفاتورة')}</div>
                    <div className="lcd-total-amount">{formatPrice(dashboardTotalDisplay.total)}</div>
                    <div className="lcd-total-items">{dashboardTotalDisplay.itemCount} {t('items', 'عناصر')}</div>
                  </div>
                ) : (
                  <div className="lcd-placeholder">
                    <div className="lcd-welcome">{t('welcomeMessage', 'مرحباً بك في نظام iCalDZ')}</div>
                    <div className="lcd-instruction">{t('scanToStart', 'امسح الباركود للبدء')}</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Action Buttons */}
      <div className="dashboard-action-buttons">
        <button 
          className="dashboard-btn dashboard-btn-sales"
          onClick={() => navigateToPage('sales')}
        >
          <div className="btn-icon">🛒</div>
          <div className="btn-content">
            <div className="btn-title">{t('salesInvoices', 'فواتير المبيعات')}</div>
            <div className="btn-subtitle">{t('manageSalesInvoices', 'إدارة فواتير المبيعات')}</div>
          </div>
          <div className="btn-shortcut">F1</div>
        </button>

        <button 
          className="dashboard-btn dashboard-btn-purchases"
          onClick={() => navigateToPage('purchases')}
        >
          <div className="btn-icon">📦</div>
          <div className="btn-content">
            <div className="btn-title">{t('purchaseInvoices', 'فواتير المشتريات')}</div>
            <div className="btn-subtitle">{t('managePurchaseInvoices', 'إدارة فواتير المشتريات')}</div>
          </div>
          <div className="btn-shortcut">F2</div>
        </button>

        <button 
          className="dashboard-btn dashboard-btn-inventory"
          onClick={() => navigateToPage('inventory')}
        >
          <div className="btn-icon">📋</div>
          <div className="btn-content">
            <div className="btn-title">{t('inventory', 'المخزون')}</div>
            <div className="btn-subtitle">{t('manageInventory', 'إدارة المخزون والمنتجات')}</div>
          </div>
          <div className="btn-shortcut">F3</div>
        </button>
      </div>

      {/* Dashboard Statistics */}
      <div className="dashboard-stats">
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">💰</div>
            <div className="stat-content">
              <h3>{t('totalSales', 'إجمالي المبيعات')}</h3>
              <div className="stat-value">{formatPrice(dashboardStats.totalSales)}</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">📄</div>
            <div className="stat-content">
              <h3>{t('totalInvoices', 'عدد الفواتير')}</h3>
              <div className="stat-value">{dashboardStats.totalInvoices}</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">📦</div>
            <div className="stat-content">
              <h3>{t('totalProducts', 'إجمالي المنتجات')}</h3>
              <div className="stat-value">{dashboardStats.totalProducts}</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">⚠️</div>
            <div className="stat-content">
              <h3>{t('lowStockProducts', 'منتجات قليلة المخزون')}</h3>
              <div className="stat-value">{dashboardStats.lowStockProducts}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Invoices */}
      <div className="dashboard-recent">
        <h3>{t('recentInvoicesOperations', 'العمليات والفواتير الأخيرة')}</h3>
        <div className="recent-invoices-table">
          <table>
            <thead>
              <tr>
                <th>{t('invoiceNumber', 'رقم الفاتورة')}</th>
                <th>{t('customer', 'العميل')}</th>
                <th>{t('date', 'التاريخ')}</th>
                <th>{t('total', 'الإجمالي')}</th>
                <th>{t('paymentMethod', 'طريقة الدفع')}</th>
              </tr>
            </thead>
            <tbody>
              {savedInvoices.slice(-5).reverse().map(invoice => (
                <tr key={invoice.id}>
                  <td>{invoice.invoiceNumber}</td>
                  <td>{invoice.customerName}</td>
                  <td>{new Date(invoice.date).toLocaleDateString('ar-DZ')}</td>
                  <td>{formatPrice(invoice.finalTotal)}</td>
                  <td>{invoice.paymentMethod}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
