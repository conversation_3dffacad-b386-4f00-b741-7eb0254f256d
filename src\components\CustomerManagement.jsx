import React, { useState, useEffect } from 'react';

const CustomerManagement = ({ 
  savedCustomers, 
  setSavedCustomers, 
  formatPrice, 
  showToast, 
  currentUser,
  t,
  currentLanguage,
  storeSettings
}) => {
  // Customer modal states
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [showEditCustomerModal, setShowEditCustomerModal] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState(null);
  const [showDeleteCustomerModal, setShowDeleteCustomerModal] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState(null);

  // Customer form data
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    notes: ''
  });

  // Customer filters
  const [customerNameFilter, setCustomerNameFilter] = useState('');
  const [customerPhoneFilter, setCustomerPhoneFilter] = useState('');

  // Save customers to localStorage
  const saveCustomers = (customers) => {
    try {
      localStorage.setItem('icaldz-customers', JSON.stringify(customers));
      setSavedCustomers(customers);
      console.log('✅ Customers saved successfully:', customers.length);
    } catch (error) {
      console.error('❌ Error saving customers:', error);
      showToast(`❌ ${t('errorSavingCustomer', 'خطأ في حفظ العميل')}`, 'error', 3000);
    }
  };

  // Reset customer form
  const resetCustomerForm = () => {
    setNewCustomer({
      name: '',
      phone: '',
      email: '',
      address: '',
      notes: ''
    });
  };

  // Handle creating new customer
  const handleCreateCustomer = () => {
    try {
      // Validate required fields
      if (!newCustomer.name.trim()) {
        showToast(`❌ ${t('customerNameRequired', 'اسم العميل مطلوب')}`, 'error', 3000);
        return;
      }

      // Check for duplicate phone number
      if (newCustomer.phone && savedCustomers.some(customer => customer.phone === newCustomer.phone)) {
        showToast(`❌ ${t('phoneNumberExists', 'رقم الهاتف موجود بالفعل')}`, 'error', 3000);
        return;
      }

      // Create customer object
      const customerToSave = {
        ...newCustomer,
        id: Date.now().toString(),
        createdAt: new Date().toISOString()
      };

      // Save customer
      const updatedCustomers = [...savedCustomers, customerToSave];
      saveCustomers(updatedCustomers);

      // Show success message
      showToast(`✅ ${t('customerCreatedSuccess', 'تم إنشاء العميل بنجاح')}`, 'success', 3000);

      // Reset form and close modal
      resetCustomerForm();
      setShowCustomerModal(false);

    } catch (error) {
      console.error('Error creating customer:', error);
      showToast(`❌ ${t('errorCreatingCustomer', 'خطأ في إنشاء العميل')}`, 'error', 3000);
    }
  };

  // Handle editing customer
  const handleEditCustomer = () => {
    try {
      if (!editingCustomer) return;

      // Validate required fields
      if (!editingCustomer.name.trim()) {
        showToast(`❌ ${t('customerNameRequired', 'اسم العميل مطلوب')}`, 'error', 3000);
        return;
      }

      // Check for duplicate phone number (excluding current customer)
      if (editingCustomer.phone && savedCustomers.some(customer => 
        customer.phone === editingCustomer.phone && customer.id !== editingCustomer.id)) {
        showToast(`❌ ${t('phoneNumberExists', 'رقم الهاتف موجود بالفعل')}`, 'error', 3000);
        return;
      }

      // Update customer
      const updatedCustomers = savedCustomers.map(customer =>
        customer.id === editingCustomer.id ? editingCustomer : customer
      );
      saveCustomers(updatedCustomers);

      // Show success message
      showToast(`✅ ${t('customerUpdatedSuccess', 'تم تحديث العميل بنجاح')}`, 'success', 3000);

      // Reset states and close modal
      setEditingCustomer(null);
      setShowEditCustomerModal(false);

    } catch (error) {
      console.error('Error editing customer:', error);
      showToast(`❌ ${t('errorEditingCustomer', 'خطأ في تعديل العميل')}`, 'error', 3000);
    }
  };

  // Handle deleting customer
  const handleDeleteCustomer = () => {
    try {
      if (!customerToDelete) return;

      // Remove customer
      const updatedCustomers = savedCustomers.filter(customer => customer.id !== customerToDelete.id);
      saveCustomers(updatedCustomers);

      // Show success message
      showToast(`✅ ${t('customerDeletedSuccess', 'تم حذف العميل بنجاح')}`, 'success', 3000);

      // Reset states and close modal
      setCustomerToDelete(null);
      setShowDeleteCustomerModal(false);

    } catch (error) {
      console.error('Error deleting customer:', error);
      showToast(`❌ ${t('errorDeletingCustomer', 'خطأ في حذف العميل')}`, 'error', 3000);
    }
  };

  // Filter customers based on current filters
  const filteredCustomers = savedCustomers.filter(customer => {
    const matchesName = !customerNameFilter || 
      customer.name.toLowerCase().includes(customerNameFilter.toLowerCase());
    const matchesPhone = !customerPhoneFilter || 
      customer.phone.includes(customerPhoneFilter);
    
    return matchesName && matchesPhone;
  });

  // Sort customers by creation date (newest first)
  const sortedCustomers = [...filteredCustomers].sort((a, b) => 
    new Date(b.createdAt) - new Date(a.createdAt)
  );

  return {
    // States
    showCustomerModal,
    setShowCustomerModal,
    showEditCustomerModal,
    setShowEditCustomerModal,
    showDeleteCustomerModal,
    setShowDeleteCustomerModal,
    editingCustomer,
    setEditingCustomer,
    customerToDelete,
    setCustomerToDelete,
    newCustomer,
    setNewCustomer,
    customerNameFilter,
    setCustomerNameFilter,
    customerPhoneFilter,
    setCustomerPhoneFilter,
    filteredCustomers: sortedCustomers,
    
    // Functions
    handleCreateCustomer,
    handleEditCustomer,
    handleDeleteCustomer,
    resetCustomerForm,
    saveCustomers
  };
};

export default CustomerManagement;
