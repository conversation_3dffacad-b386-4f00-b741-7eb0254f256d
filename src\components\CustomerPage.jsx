import React from 'react';

const CustomerPage = ({ customerManagement, currentLanguage, t, formatPrice }) => {
  const {
    // States
    showCustomerModal,
    setShowCustomerModal,
    showEditCustomerModal,
    setShowEditCustomerModal,
    showDeleteCustomerModal,
    setShowDeleteCustomerModal,
    editingCustomer,
    setEditingCustomer,
    customerToDelete,
    setCustomerToDelete,
    newCustomer,
    setNewCustomer,
    customerNameFilter,
    setCustomerNameFilter,
    customerPhoneFilter,
    setCustomerPhoneFilter,
    filteredCustomers,
    
    // Functions
    handleCreateCustomer,
    handleEditCustomer,
    handleDeleteCustomer,
    resetCustomerForm
  } = customerManagement;

  return (
    <div className="customers-page">
      <div className={`page-header ${currentLanguage !== 'ar' ? 'page-header-ltr-split' : ''}`}>
        <div className="page-title-section">
          <h1>👥 {t('customersManagement', 'إدارة الزبائن')}</h1>
        </div>
        <div className="page-description-section">
          <button className="btn btn-primary" onClick={() => setShowCustomerModal(true)}>
            + {t('addNewCustomer', 'إضافة زبون جديد')}
          </button>
        </div>
      </div>

      {/* Customer Filters */}
      <div className="filters-section">
        <div className="filters-row">
          <div className="filter-group">
            <label>{t('filterByName', 'تصفية حسب الاسم')}</label>
            <input
              type="text"
              value={customerNameFilter}
              onChange={(e) => setCustomerNameFilter(e.target.value)}
              placeholder={t('customerName', 'اسم العميل')}
            />
          </div>

          <div className="filter-group">
            <label>{t('filterByPhone', 'تصفية حسب الهاتف')}</label>
            <input
              type="text"
              value={customerPhoneFilter}
              onChange={(e) => setCustomerPhoneFilter(e.target.value)}
              placeholder={t('phoneNumber', 'رقم الهاتف')}
            />
          </div>

          <div className="filter-actions">
            <button
              className="btn btn-secondary"
              onClick={() => {
                setCustomerNameFilter('');
                setCustomerPhoneFilter('');
              }}
            >
              🗑️ {t('clearFilters', 'مسح المرشحات')}
            </button>
          </div>
        </div>
      </div>

      {/* Customer Summary */}
      <div className="summary-cards">
        <div className="summary-card">
          <div className="summary-icon">👥</div>
          <div className="summary-content">
            <div className="summary-number">{filteredCustomers.length}</div>
            <div className="summary-label">{t('totalCustomers', 'إجمالي العملاء')}</div>
          </div>
        </div>
      </div>

      {/* Customer Table */}
      <div className="table-section">
        <div className="table-container">
          <table className="data-table">
            <thead>
              <tr>
                <th>{t('customerName', 'اسم العميل')}</th>
                <th>{t('phoneNumber', 'رقم الهاتف')}</th>
                <th>{t('email', 'البريد الإلكتروني')}</th>
                <th>{t('address', 'العنوان')}</th>
                <th>{t('notes', 'ملاحظات')}</th>
                <th>{t('createdAt', 'تاريخ الإنشاء')}</th>
                <th>{t('actions', 'الإجراءات')}</th>
              </tr>
            </thead>
            <tbody>
              {filteredCustomers.map((customer) => (
                <tr key={customer.id}>
                  <td>
                    <div className="customer-name">
                      <strong>{customer.name}</strong>
                    </div>
                  </td>
                  <td>
                    <div className="customer-phone">
                      {customer.phone || t('noPhone', 'لا يوجد هاتف')}
                    </div>
                  </td>
                  <td>
                    <div className="customer-email">
                      {customer.email || t('noEmail', 'لا يوجد بريد إلكتروني')}
                    </div>
                  </td>
                  <td>
                    <div className="customer-address">
                      {customer.address || t('noAddress', 'لا يوجد عنوان')}
                    </div>
                  </td>
                  <td>
                    <div className="customer-notes">
                      {customer.notes || t('noNotes', 'لا توجد ملاحظات')}
                    </div>
                  </td>
                  <td>
                    <div className="customer-date">
                      {new Date(customer.createdAt).toLocaleDateString(
                        currentLanguage === 'ar' ? 'ar-DZ' : 
                        currentLanguage === 'fr' ? 'fr-FR' : 'en-US'
                      )}
                    </div>
                  </td>
                  <td>
                    <div className="customer-actions">
                      <button
                        className="btn btn-sm btn-info"
                        onClick={() => {
                          setEditingCustomer(customer);
                          setShowEditCustomerModal(true);
                        }}
                        title={t('editCustomer', 'تعديل العميل')}
                      >
                        ✏️
                      </button>
                      <button
                        className="btn btn-sm btn-danger"
                        onClick={() => {
                          setCustomerToDelete(customer);
                          setShowDeleteCustomerModal(true);
                        }}
                        title={t('deleteCustomer', 'حذف العميل')}
                      >
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              ))}

              {filteredCustomers.length === 0 && (
                <tr>
                  <td colSpan="7" style={{ textAlign: 'center', padding: '40px' }}>
                    <div className="no-data-message">
                      <div className="no-data-icon">👥</div>
                      <h3>{t('noCustomersFound', 'لا توجد عملاء')}</h3>
                      <p>{t('createFirstCustomer', 'قم بإنشاء أول عميل باستخدام زر "إضافة زبون جديد"')}</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Customer Modal */}
      {showCustomerModal && (
        <div className="modal-overlay" onClick={() => setShowCustomerModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>{t('addNewCustomer', 'إضافة زبون جديد')}</h3>
              <button className="modal-close" onClick={() => setShowCustomerModal(false)}>×</button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>{t('customerName', 'اسم العميل')} *</label>
                <input
                  type="text"
                  value={newCustomer.name}
                  onChange={(e) => setNewCustomer({...newCustomer, name: e.target.value})}
                  placeholder={t('enterCustomerName', 'أدخل اسم العميل')}
                  required
                />
              </div>
              <div className="form-group">
                <label>{t('phoneNumber', 'رقم الهاتف')}</label>
                <input
                  type="tel"
                  value={newCustomer.phone}
                  onChange={(e) => setNewCustomer({...newCustomer, phone: e.target.value})}
                  placeholder={t('enterPhoneNumber', 'أدخل رقم الهاتف')}
                />
              </div>
              <div className="form-group">
                <label>{t('email', 'البريد الإلكتروني')}</label>
                <input
                  type="email"
                  value={newCustomer.email}
                  onChange={(e) => setNewCustomer({...newCustomer, email: e.target.value})}
                  placeholder={t('enterEmail', 'أدخل البريد الإلكتروني')}
                />
              </div>
              <div className="form-group">
                <label>{t('address', 'العنوان')}</label>
                <textarea
                  value={newCustomer.address}
                  onChange={(e) => setNewCustomer({...newCustomer, address: e.target.value})}
                  placeholder={t('enterAddress', 'أدخل العنوان')}
                  rows="3"
                />
              </div>
              <div className="form-group">
                <label>{t('notes', 'ملاحظات')}</label>
                <textarea
                  value={newCustomer.notes}
                  onChange={(e) => setNewCustomer({...newCustomer, notes: e.target.value})}
                  placeholder={t('enterNotes', 'أدخل ملاحظات')}
                  rows="3"
                />
              </div>
            </div>
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={() => setShowCustomerModal(false)}>
                {t('cancel', 'إلغاء')}
              </button>
              <button className="btn btn-primary" onClick={handleCreateCustomer}>
                {t('addCustomer', 'إضافة العميل')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Customer Modal */}
      {showEditCustomerModal && editingCustomer && (
        <div className="modal-overlay" onClick={() => setShowEditCustomerModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>{t('editCustomer', 'تعديل العميل')}</h3>
              <button className="modal-close" onClick={() => setShowEditCustomerModal(false)}>×</button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>{t('customerName', 'اسم العميل')} *</label>
                <input
                  type="text"
                  value={editingCustomer.name}
                  onChange={(e) => setEditingCustomer({...editingCustomer, name: e.target.value})}
                  placeholder={t('enterCustomerName', 'أدخل اسم العميل')}
                  required
                />
              </div>
              <div className="form-group">
                <label>{t('phoneNumber', 'رقم الهاتف')}</label>
                <input
                  type="tel"
                  value={editingCustomer.phone}
                  onChange={(e) => setEditingCustomer({...editingCustomer, phone: e.target.value})}
                  placeholder={t('enterPhoneNumber', 'أدخل رقم الهاتف')}
                />
              </div>
              <div className="form-group">
                <label>{t('email', 'البريد الإلكتروني')}</label>
                <input
                  type="email"
                  value={editingCustomer.email}
                  onChange={(e) => setEditingCustomer({...editingCustomer, email: e.target.value})}
                  placeholder={t('enterEmail', 'أدخل البريد الإلكتروني')}
                />
              </div>
              <div className="form-group">
                <label>{t('address', 'العنوان')}</label>
                <textarea
                  value={editingCustomer.address}
                  onChange={(e) => setEditingCustomer({...editingCustomer, address: e.target.value})}
                  placeholder={t('enterAddress', 'أدخل العنوان')}
                  rows="3"
                />
              </div>
              <div className="form-group">
                <label>{t('notes', 'ملاحظات')}</label>
                <textarea
                  value={editingCustomer.notes}
                  onChange={(e) => setEditingCustomer({...editingCustomer, notes: e.target.value})}
                  placeholder={t('enterNotes', 'أدخل ملاحظات')}
                  rows="3"
                />
              </div>
            </div>
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={() => setShowEditCustomerModal(false)}>
                {t('cancel', 'إلغاء')}
              </button>
              <button className="btn btn-primary" onClick={handleEditCustomer}>
                {t('updateCustomer', 'تحديث العميل')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Customer Modal */}
      {showDeleteCustomerModal && customerToDelete && (
        <div className="modal-overlay" onClick={() => setShowDeleteCustomerModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>{t('deleteCustomer', 'حذف العميل')}</h3>
              <button className="modal-close" onClick={() => setShowDeleteCustomerModal(false)}>×</button>
            </div>
            <div className="modal-body">
              <p>{t('confirmDeleteCustomer', 'هل أنت متأكد من حذف هذا العميل؟')}</p>
              <p><strong>{customerToDelete.name}</strong></p>
            </div>
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={() => setShowDeleteCustomerModal(false)}>
                {t('cancel', 'إلغاء')}
              </button>
              <button className="btn btn-danger" onClick={handleDeleteCustomer}>
                {t('delete', 'حذف')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerPage;
