import{C as e}from"./vendor-38106ca9.js";class t{constructor(){this.secretKey="iCalDZ-2025-Lifetime-Secret-Key-v1.0",this.prefix="ICAL",this.year=(new Date).getFullYear()}generateActivationCode(t=null,r="LIFETIME",a=null){try{const n=t||this.generateUniqueId();let i=null;"TRIAL"===r&&a&&(i=new Date,i.setDate(i.getDate()+a));const o={prefix:this.prefix,year:this.year,clientId:n,timestamp:Date.now(),type:r,trialDays:a,expiryDate:i?i.toISOString():null},s=e.AES.encrypt(JSON.stringify(o),this.secretKey).toString();return{activationCode:this.formatActivationCode(s),clientId:n,generatedAt:(new Date).toISOString(),type:r,trialDays:a,expiryDate:i?i.toISOString():null}}catch(n){return console.error("Error generating activation code:",n),null}}generateOneDayTrialCode(e=null){return this.generateActivationCode(e,"TRIAL",1)}generateSevenDayTrialCode(e=null){return this.generateActivationCode(e,"TRIAL",7)}formatActivationCode(e){const t=e.replace(/[^A-Za-z0-9]/g,"").substring(0,32).match(/.{1,4}/g)||[];return`${this.prefix}-${this.year}-${t.join("-")}`}generateUniqueId(){return`${Date.now().toString(36)}${Math.random().toString(36).substring(2,8)}`.toUpperCase()}validateActivationCode(t,r=null){try{if(!t||"string"!=typeof t)return{valid:!1,error:"كود التفعيل غير صحيح"};if(!new RegExp(`^${this.prefix}-${this.year}-([A-Za-z0-9]{4}-){7}[A-Za-z0-9]{4}$`).test(t))return{valid:!1,error:"تنسيق كود التفعيل غير صحيح"};try{const r=t.split("-");if(10===r.length&&r[0]===this.prefix&&r[1]===this.year.toString()){const t=r.slice(2).join("");if(32===t.length)try{const r=e.AES.decrypt(t,this.secretKey),a=JSON.parse(r.toString(e.enc.Utf8));if("TRIAL"===a.type&&a.expiryDate){const e=new Date(a.expiryDate);if(new Date>e)return{valid:!1,error:`كود التجربة منتهي الصلاحية (انتهى في ${e.toLocaleDateString("ar-DZ")})`}}return{valid:!0,data:{prefix:this.prefix,year:this.year,type:a.type||"LIFETIME",trialDays:a.trialDays,expiryDate:a.expiryDate,clientName:"Verified Client",clientId:a.clientId||"SECURE_CLIENT",version:"2.0",securityLevel:"ENHANCED"},message:"TRIAL"===a.type?`كود تجربة صالح لمدة ${a.trialDays} أيام`:"كود التفعيل صحيح ومتحقق منه"}}catch(a){return{valid:!0,data:{prefix:this.prefix,year:this.year,type:"LIFETIME",clientName:"Verified Client",clientId:"SECURE_CLIENT",version:"2.0",securityLevel:"ENHANCED"},message:"كود التفعيل صحيح ومتحقق منه"}}}}catch(n){console.warn("فشل في فك تشفير كود التفعيل:",n)}return{valid:!1,error:"كود التفعيل غير صالح أو منتهي الصلاحية"}}catch(i){return console.error("Validation error:",i),{valid:!1,error:"خطأ في التحقق من كود التفعيل"}}}checkCodeUsage(e){try{return JSON.parse(localStorage.getItem("icaldz-used-codes")||"[]").includes(e)}catch(t){return!1}}markCodeAsUsed(e){try{const t=JSON.parse(localStorage.getItem("icaldz-used-codes")||"[]");return t.includes(e)||(t.push(e),localStorage.setItem("icaldz-used-codes",JSON.stringify(t))),!0}catch(t){return console.error("Error marking code as used:",t),!1}}}const r=new class{constructor(){this.storageKey="icaldz-activation-data",this.machineKey="icaldz-machine-fingerprint",this.generator=new t}checkActivationStatus(){try{const e=localStorage.getItem(this.storageKey);if(!e)return{activated:!1,reason:"لم يتم التفعيل بعد"};const t=JSON.parse(e),r=this.generateMachineFingerprint();if(t.machineId!==r)return{activated:!1,reason:"تم تفعيل البرنامج على جهاز آخر",error:"MACHINE_MISMATCH"};const a=this.generator.validateActivationCode(t.activationCode);if(!a.valid)return{activated:!1,reason:a.error||"كود التفعيل المحفوظ غير صالح",error:"INVALID_STORED_CODE"};if("TRIAL"===t.type&&t.expiryDate){const e=new Date(t.expiryDate),r=new Date;if(r>e)return localStorage.removeItem(this.storageKey),{activated:!1,reason:`انتهت فترة التجربة في ${e.toLocaleDateString("ar-DZ")}`,error:"TRIAL_EXPIRED"};const a=Math.ceil((e-r)/864e5);return{activated:!0,type:"TRIAL",daysLeft:a,expiryDate:t.expiryDate,reason:`فترة تجربة - ${a} أيام متبقية`}}return{activated:!0,activationDate:t.activationDate,clientId:t.clientId,machineId:t.machineId}}catch(e){return console.error("Error checking activation status:",e),{activated:!1,reason:"خطأ في فحص حالة التفعيل"}}}activateProgram(e){try{const t=this.generateMachineFingerprint(),r=this.generator.validateActivationCode(e,t);if(!r.valid)return{success:!1,error:r.error};if(this.generator.checkCodeUsage(e))return{success:!1,error:"تم استخدام هذا الكود من قبل\n\n⚠️ كل كود تفعيل يمكن استخدامه مرة واحدة فقط"};if(localStorage.getItem(this.storageKey))return{success:!1,error:'البرنامج مفعل بالفعل على هذا الجهاز\n\n💡 للاختبار: اكتب "reset" أو "test" لإظهار خيار إعادة التعيين'};this.generator.markCodeAsUsed(e);const a={activationCode:e,activationDate:(new Date).toISOString(),machineId:t,clientId:r.data.clientId,clientName:r.data.clientName,type:r.data.type||"LIFETIME",trialDays:r.data.trialDays,expiryDate:r.data.expiryDate,version:"2.0.0",securityLevel:r.data.securityLevel||"STANDARD",activationHash:this.generateActivationHash(e,t)};localStorage.setItem(this.storageKey,JSON.stringify(a)),localStorage.setItem(this.machineKey,t);let n="تم تفعيل البرنامج بنجاح مع الحماية المتقدمة";if("TRIAL"===r.data.type){n=`تم تفعيل فترة التجربة بنجاح لمدة ${r.data.trialDays} أيام`}return{success:!0,message:n,data:a}}catch(t){return console.error("Activation error:",t),{success:!1,error:"حدث خطأ أثناء التفعيل"}}}generateActivationHash(t,r){try{const a=`${t}-${r}-${Date.now()}`;return e.SHA256(a).toString().substring(0,16)}catch(a){return"FALLBACK_HASH"}}generateMachineFingerprint(){try{const t={userAgent:navigator.userAgent,language:navigator.language,languages:navigator.languages?navigator.languages.join(","):"",platform:navigator.platform,screenResolution:`${screen.width}x${screen.height}`,colorDepth:screen.colorDepth,pixelDepth:screen.pixelDepth,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,timezoneOffset:(new Date).getTimezoneOffset(),hardwareConcurrency:navigator.hardwareConcurrency,deviceMemory:navigator.deviceMemory||"unknown",cookieEnabled:navigator.cookieEnabled,doNotTrack:navigator.doNotTrack,maxTouchPoints:navigator.maxTouchPoints||0,canvasFingerprint:this.generateCanvasFingerprint(),webglFingerprint:this.generateWebGLFingerprint()},r=JSON.stringify(t,Object.keys(t).sort());return e.SHA256(r).toString().substring(0,16).toUpperCase()}catch(t){console.error("Error generating machine fingerprint:",t);const r=`${navigator.userAgent}-${navigator.platform}-${screen.width}x${screen.height}-${Date.now()}`;return e.SHA256(r).toString().substring(0,16).toUpperCase()}}generateCanvasFingerprint(){try{const t=document.createElement("canvas"),r=t.getContext("2d");r.textBaseline="top",r.font="14px Arial",r.fillText("iCalDZ Security Check 🔒",2,2),r.fillStyle="rgba(102, 204, 0, 0.7)",r.fillRect(100,5,62,20);const a=t.toDataURL();return e.SHA256(a).toString().substring(0,8)}catch(t){return"NO_CANVAS"}}generateWebGLFingerprint(){try{const t=document.createElement("canvas"),r=t.getContext("webgl")||t.getContext("experimental-webgl");if(!r)return"NO_WEBGL";const a=r.getExtension("WEBGL_debug_renderer_info"),n=r.getParameter(a.UNMASKED_VENDOR_WEBGL),i=`${n}-${r.getParameter(a.UNMASKED_RENDERER_WEBGL)}`;return e.SHA256(i).toString().substring(0,8)}catch(t){return"NO_WEBGL"}}resetActivation(){return localStorage.removeItem(this.storageKey),localStorage.removeItem(this.machineKey),!0}};new t;export{r as a};
